# 和平自留地 🏠

> 如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹

[![Website](https://img.shields.io/website?url=https%3A//hepingfly.github.io)](https://hepingfly.github.io)
[![GitHub last commit](https://img.shields.io/github/last-commit/hepingfly/hepingfly.github.io)](https://github.com/hepingfly/hepingfly.github.io)
[![GitHub issues](https://img.shields.io/github/issues/hepingfly/hepingfly.github.io)](https://github.com/hepingfly/hepingfly.github.io/issues)

一个专注于个人品牌建设、读书分享和思维成长的个人博客项目，包含两套完整的博客系统实现。

## 🌟 项目概览

本项目包含两个独立的博客系统：

### 1. Gmeek 博客系统（主站）
- **访问地址**: [https://hepingfly.github.io](https://hepingfly.github.io)
- **技术栈**: 基于 [Gmeek](https://github.com/Meekdai/Gmeek) 的静态博客生成器
- **特点**: 使用 GitHub Issues 作为 CMS，自动生成静态网站
- **文章数量**: 4 篇
- **最后更新**: 2024-08-04

### 2. Next.js 博客系统（现代化版本）
- **目录**: `/blog-nextjs`
- **技术栈**: Next.js 15 + TypeScript + Tailwind CSS
- **特点**: 现代化 UI 设计，完整的博客功能
- **状态**: 开发中

## 📝 博客内容

专注于以下主题：
- 🎯 **个人IP建设** - 个人品牌打造与运营策略
- 📚 **读书分享** - 优质书籍推荐与读书心得
- 🧠 **思维成长** - 认知升级与思维模式分享
- ✍️ **随笔感悟** - 生活感悟与个人思考

## 🛠️ 技术架构

### Gmeek 博客系统
```
├── docs/                 # 生成的静态网站文件
├── static/              # 静态资源文件
├── backup/              # 文章备份
├── config.json          # Gmeek 配置文件
└── blogBase.json        # 博客基础数据
```

### Next.js 博客系统
```
blog-nextjs/
├── src/
│   ├── app/             # Next.js App Router
│   ├── components/      # React 组件
│   ├── lib/            # 工具库和配置
│   └── types/          # TypeScript 类型定义
├── public/             # 静态资源
└── package.json        # 项目依赖
```

## 🚀 快速开始

### 查看 Gmeek 博客
直接访问：[https://hepingfly.github.io](https://hepingfly.github.io)

### 本地运行 Next.js 博客
```bash
# 进入 Next.js 项目目录
cd blog-nextjs

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:3000
```

## 📊 项目统计

- **总文章数**: 4 篇
- **主要标签**: 个人IP、随笔
- **建站时间**: 2024年6月24日
- **访问量**: 7,582+
- **GitHub Stars**: 欢迎 ⭐

## 🔗 相关链接

- **主站**: [https://hepingfly.github.io](https://hepingfly.github.io)
- **GitHub**: [@hepingfly](https://github.com/hepingfly)
- **标签页**: [文章分类](https://hepingfly.github.io/tag.html)
- **打赏页**: [支持作者](https://hepingfly.github.io/reward.html)

## 📞 联系方式

- **作者**: 和平
- **邮箱**: 通过 GitHub Issues 联系
- **博客**: [https://hepingfly.github.io](https://hepingfly.github.io)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

### 🔄 自动更新

- **Gmeek 博客**: 通过 GitHub Actions 自动更新
- **统计信息**: 最后更新于 2024-08-04 04:06:55
- **技术支持**: Powered by ❤️ [Gmeek](https://github.com/Meekdai/Gmeek)
