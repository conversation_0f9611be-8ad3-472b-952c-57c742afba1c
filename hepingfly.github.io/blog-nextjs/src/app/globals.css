@import "tailwindcss";
@import "tw-animate-css";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Monaco, Consolas, monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.5rem;
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
  --primary: #14b8a6;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #171717;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #f0fdfa;
  --accent-foreground: #0f766e;
  --destructive: #ef4444;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #14b8a6;
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: #0a0a0a;
  --foreground: #fafafa;
  --card: #171717;
  --card-foreground: #fafafa;
  --popover: #171717;
  --popover-foreground: #fafafa;
  --primary: #2dd4bf;
  --primary-foreground: #042f2e;
  --secondary: #262626;
  --secondary-foreground: #fafafa;
  --muted: #262626;
  --muted-foreground: #737373;
  --accent: #134e4a;
  --accent-foreground: #5eead4;
  --destructive: #ef4444;
  --border: #404040;
  --input: #404040;
  --ring: #2dd4bf;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }

  /* 改善文本渲染 */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  /* 链接样式 */
  a {
    @apply text-primary hover:text-primary/80 transition-colors duration-200;
  }

  /* 代码样式 */
  code {
    @apply font-mono text-sm bg-muted px-1.5 py-0.5 rounded-md;
  }

  pre {
    @apply font-mono text-sm bg-muted p-4 rounded-lg overflow-x-auto;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border/80;
  }
}

@layer components {
  /* 自定义动画 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
  }

  .dark .gradient-bg {
    background: linear-gradient(135deg, #042f2e 0%, #134e4a 100%);
  }

  /* 玻璃效果 */
  .glass {
    @apply bg-background/80 backdrop-blur-md border border-border/50;
  }

  /* 卡片悬停效果 */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  /* 网格背景 */
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .dark .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  }

  /* 文本截断 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Markdown 内容样式 */
  .markdown-content {
    @apply text-foreground leading-relaxed;
  }

  .markdown-content h1,
  .markdown-content h2,
  .markdown-content h3,
  .markdown-content h4,
  .markdown-content h5,
  .markdown-content h6 {
    @apply font-semibold text-foreground mt-8 mb-4 tracking-tight;
  }

  .markdown-content h1 {
    @apply text-3xl;
  }

  .markdown-content h2 {
    @apply text-2xl;
  }

  .markdown-content h3 {
    @apply text-xl;
  }

  .markdown-content h4 {
    @apply text-lg;
  }

  .markdown-content p {
    @apply mb-4 leading-7;
  }

  .markdown-content ul,
  .markdown-content ol {
    @apply mb-4 ml-6;
  }

  .markdown-content li {
    @apply mb-2;
  }

  .markdown-content blockquote {
    @apply border-l-4 border-primary pl-4 italic text-muted-foreground my-6;
  }

  .markdown-content code {
    @apply bg-muted px-2 py-1 rounded text-sm font-mono;
  }

  .markdown-content pre {
    @apply bg-muted p-4 rounded-lg overflow-x-auto my-6;
  }

  .markdown-content pre code {
    @apply bg-transparent p-0;
  }

  .markdown-content a {
    @apply text-primary hover:text-primary/80 underline underline-offset-4;
  }

  .markdown-content img {
    @apply rounded-lg shadow-sm my-6 max-w-full h-auto;
  }

  .markdown-content hr {
    @apply border-border my-8;
  }

  .markdown-content table {
    @apply w-full border-collapse border border-border my-6;
  }

  .markdown-content th,
  .markdown-content td {
    @apply border border-border px-4 py-2 text-left;
  }

  .markdown-content th {
    @apply bg-muted font-semibold;
  }

  /* 移动端优化 */
  @media (max-width: 640px) {
    .markdown-content {
      @apply text-sm leading-6;
    }

    .markdown-content h1 {
      @apply text-2xl;
    }

    .markdown-content h2 {
      @apply text-xl;
    }

    .markdown-content h3 {
      @apply text-lg;
    }

    .markdown-content pre {
      @apply p-3 text-xs;
    }

    .markdown-content table {
      @apply text-sm;
    }

    .markdown-content th,
    .markdown-content td {
      @apply px-2 py-1;
    }
  }

  /* 触摸设备优化 */
  @media (hover: none) and (pointer: coarse) {
    .card-hover:hover {
      @apply transform-none shadow-none;
    }

    .card-hover:active {
      @apply transform scale-95 shadow-lg;
    }
  }

  /* 安全区域适配 */
  @supports (padding: max(0px)) {
    .safe-area-inset-x {
      padding-left: max(1rem, env(safe-area-inset-left));
      padding-right: max(1rem, env(safe-area-inset-right));
    }

    .safe-area-inset-bottom {
      padding-bottom: max(1rem, env(safe-area-inset-bottom));
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
