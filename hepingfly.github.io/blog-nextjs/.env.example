# GitHub API Token (可选，用于提高API限制)
# 在 GitHub Settings > Developer settings > Personal access tokens 创建
GITHUB_TOKEN=your_github_token_here

# 网站配置
NEXT_PUBLIC_SITE_URL=https://hepingfly.github.io
NEXT_PUBLIC_SITE_TITLE=和平自留地
NEXT_PUBLIC_SITE_DESCRIPTION=如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹

# GitHub 仓库配置
NEXT_PUBLIC_GITHUB_OWNER=hepingfly
NEXT_PUBLIC_GITHUB_REPO=hepingfly.github.io

# Google Analytics (可选)
NEXT_PUBLIC_GA_ID=G-PB7Y2QXTLR

# 其他配置
NEXT_PUBLIC_POSTS_PER_PAGE=10
NEXT_PUBLIC_EXCERPT_LENGTH=200
