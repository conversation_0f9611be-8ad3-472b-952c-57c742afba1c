(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[729],{1182:(e,s,a)=>{Promise.resolve().then(a.bind(a,6705))},1366:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]])},4186:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5169:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6705:(e,s,a)=>{"use strict";a.d(s,{default:()=>A});var l=a(5155),r=a(2115),t=a(6874),i=a.n(t),c=a(8274),n=a(5169),d=a(3332),o=a(9074),m=a(4186),x=a(1366),h=a(9946);let p=(0,h.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var j=a(9099),u=a(1976);let g=(0,h.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var f=a(285),N=a(6126),y=a(6695),v=a(1394),w=a(8130),b=a(8816),k=a(5950);function A(e){let{post:s}=e,[a,t]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=()=>{t(window.scrollY>400)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let h=async()=>{if(navigator.share)try{await navigator.share({title:s.title,text:s.excerpt||"来自和平自留地的文章",url:window.location.href})}catch(e){console.log("分享失败:",e)}else navigator.clipboard.writeText(window.location.href)};return(0,l.jsx)(w.default,{children:(0,l.jsxs)("article",{className:"min-h-screen bg-background",children:[(0,l.jsx)("section",{className:"py-8 bg-muted/30",children:(0,l.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"space-y-6",children:[(0,l.jsx)(f.$,{variant:"ghost",asChild:!0,className:"group",children:(0,l.jsxs)(i(),{href:"/posts",children:[(0,l.jsx)(n.A,{className:"h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform"}),"返回文章列表"]})}),(0,l.jsxs)("div",{className:"space-y-6",children:[s.labels.length>0&&(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:s.labels.map(e=>(0,l.jsx)(N.E,{variant:"outline",className:"hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer",asChild:!0,children:(0,l.jsxs)(i(),{href:"/tag/".concat(encodeURIComponent(e)),children:[(0,l.jsx)(d.A,{className:"h-3 w-3 mr-1"}),e]})},e))}),(0,l.jsx)("h1",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground leading-tight",children:s.title}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-6 text-sm text-muted-foreground",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(o.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{children:(0,b.Yq)(s.created_at)})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[s.readingTime&&(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(m.A,{className:"h-4 w-4"}),(0,l.jsxs)("span",{children:[s.readingTime," 分钟阅读"]})]}),s.comments>0&&(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(x.A,{className:"h-4 w-4"}),(0,l.jsxs)("span",{children:[s.comments," 条评论"]})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)(f.$,{variant:"outline",size:"sm",onClick:h,className:"group",children:[(0,l.jsx)(p,{className:"h-4 w-4 sm:mr-2 group-hover:scale-110 transition-transform"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"分享"})]}),(0,l.jsx)(f.$,{variant:"outline",size:"sm",asChild:!0,children:(0,l.jsxs)("a",{href:s.html_url,target:"_blank",rel:"noopener noreferrer",children:[(0,l.jsx)(j.A,{className:"h-4 w-4 sm:mr-2"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"GitHub"})]})})]})]})]})]})})}),(0,l.jsx)("section",{className:"py-12",children:(0,l.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,l.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"lg:col-span-3 order-2 lg:order-1",children:[(0,l.jsx)(y.Zp,{className:"prose prose-sm sm:prose-lg max-w-none dark:prose-invert",children:(0,l.jsx)(y.Wu,{className:"p-4 sm:p-6 lg:p-8",children:(0,l.jsx)("div",{className:"markdown-content",dangerouslySetInnerHTML:{__html:s.body.replace(/\n/g,"<br />")}})})}),(0,l.jsx)("div",{className:"mt-8 pt-8 border-t border-border",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"text-sm text-muted-foreground",children:["最后更新于 ",(0,b.fw)(s.updated_at)]}),(0,l.jsx)("div",{className:"flex items-center space-x-4",children:(0,l.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:h,className:"group",children:[(0,l.jsx)(u.A,{className:"h-4 w-4 mr-2 group-hover:text-red-500 transition-colors"}),"喜欢这篇文章"]})})]})})]}),(0,l.jsx)(c.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.4},className:"lg:col-span-1 order-1 lg:order-2",children:(0,l.jsxs)("div",{className:"lg:sticky lg:top-24 space-y-6",children:[(0,l.jsx)(y.Zp,{children:(0,l.jsxs)(y.Wu,{className:"p-6 text-center",children:[(0,l.jsxs)(v.eu,{className:"h-16 w-16 mx-auto mb-4",children:[(0,l.jsx)(v.BK,{src:k.wj.avatarUrl,alt:"作者头像"}),(0,l.jsx)(v.q5,{children:"和"})]}),(0,l.jsx)("h3",{className:"font-semibold text-foreground mb-2",children:"和平"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"个人品牌建设者 \xb7 终身学习者"}),(0,l.jsx)(f.$,{size:"sm",asChild:!0,className:"w-full",children:(0,l.jsx)(i(),{href:"/about",children:"了解更多"})})]})}),(0,l.jsx)(y.Zp,{children:(0,l.jsxs)(y.Wu,{className:"p-6",children:[(0,l.jsx)("h4",{className:"font-semibold text-foreground mb-4",children:"文章信息"}),(0,l.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-muted-foreground",children:"发布时间"}),(0,l.jsx)("span",{className:"text-foreground",children:(0,b.Yq)(s.created_at)})]}),s.readingTime&&(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-muted-foreground",children:"阅读时间"}),(0,l.jsxs)("span",{className:"text-foreground",children:[s.readingTime," 分钟"]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-muted-foreground",children:"字数统计"}),(0,l.jsxs)("span",{className:"text-foreground",children:[s.body.length," 字符"]})]}),s.comments>0&&(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-muted-foreground",children:"评论数"}),(0,l.jsx)("span",{className:"text-foreground",children:s.comments})]})]})]})}),s.labels.length>0&&(0,l.jsx)(y.Zp,{children:(0,l.jsxs)(y.Wu,{className:"p-6",children:[(0,l.jsx)("h4",{className:"font-semibold text-foreground mb-4",children:"相关标签"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:s.labels.map(e=>(0,l.jsx)(N.E,{variant:"secondary",className:"hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer",asChild:!0,children:(0,l.jsx)(i(),{href:"/tag/".concat(encodeURIComponent(e)),children:e})},e))})]})})]})})]})})}),a&&(0,l.jsx)(c.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"fixed bottom-8 right-8 z-50",children:(0,l.jsx)(f.$,{size:"sm",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"rounded-full shadow-lg",children:(0,l.jsx)(g,{className:"h-4 w-4"})})})]})})}},9074:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{e.O(0,[458,707,788,441,964,358],()=>e(e.s=1182)),_N_E=e.O()}]);