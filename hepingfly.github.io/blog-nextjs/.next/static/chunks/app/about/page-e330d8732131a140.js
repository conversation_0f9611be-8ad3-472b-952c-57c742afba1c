(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{463:(e,s,i)=>{"use strict";i.d(s,{A:()=>a});let a=(0,i(9946).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},3626:(e,s,i)=>{"use strict";i.d(s,{default:()=>g});var a=i(5155);i(2115);var t=i(8274),r=i(5040),l=i(1007),n=i(463),c=i(1976),d=i(8883),x=i(9099),o=i(7312),m=i(8304),h=i(285),p=i(6695),j=i(6126),u=i(1394),y=i(8130),f=i(5950);let N=["个人品牌建设","内容创作","读书分享","思维模式","学习方法","认知升级"],v=[{name:"阅读",icon:r.A,description:"热爱阅读各类书籍，特别是个人成长和思维类"},{name:"写作",icon:l.A,description:"通过写作分享思考和感悟，记录成长历程"},{name:"学习",icon:n.A,description:"保持终身学习的心态，不断探索新知识"},{name:"分享",icon:c.A,description:"乐于分享有价值的内容和经验"}],w=[{year:"2024",title:"开始个人IP建设",description:'创建"和平自留地"博客，专注于个人品牌建设和读书分享'},{year:"2023",title:"深度阅读实践",description:"开始系统性阅读个人成长类书籍，并记录读书心得"},{year:"2022",title:"思维模式探索",description:"开始关注思维模式和认知升级，探索高效学习方法"}];function g(){return(0,a.jsx)(y.default,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,a.jsx)("section",{className:"py-20 bg-muted/30",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center space-y-8",children:[(0,a.jsxs)(u.eu,{className:"h-32 w-32 mx-auto ring-4 ring-primary/20 ring-offset-4 ring-offset-background",children:[(0,a.jsx)(u.BK,{src:f.wj.avatarUrl,alt:"和平"}),(0,a.jsx)(u.q5,{className:"text-4xl font-bold",children:"和"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h1",{className:"text-4xl sm:text-5xl font-bold text-foreground",children:"你好，我是和平"}),(0,a.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed",children:"个人品牌建设者 \xb7 终身学习者 \xb7 内容创作者"}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-2 pt-4",children:N.map(e=>(0,a.jsx)(j.E,{variant:"secondary",children:e},e))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsx)(h.$,{asChild:!0,children:(0,a.jsxs)("a",{href:"mailto:<EMAIL>",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"联系我"]})}),(0,a.jsx)(h.$,{variant:"outline",asChild:!0,children:(0,a.jsxs)("a",{href:"https://github.com/hepingfly",target:"_blank",rel:"noopener noreferrer",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"GitHub"]})})]})]})})}),(0,a.jsx)("section",{className:"py-20",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 space-y-16",children:[(0,a.jsx)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:(0,a.jsxs)(p.Zp,{children:[(0,a.jsx)(p.aR,{children:(0,a.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.A,{className:"h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"关于我"})]})}),(0,a.jsxs)(p.Wu,{className:"prose prose-lg max-w-none dark:prose-invert",children:[(0,a.jsx)("p",{children:"欢迎来到我的个人空间！我是一个热爱学习和分享的人，专注于个人品牌建设、读书心得和思维成长。"}),(0,a.jsx)("p",{children:"我相信每个人都可以拥有自己的个人品牌，通过持续的学习和分享，我们可以不断提升自己的认知水平， 实现个人价值的最大化。"}),(0,a.jsx)("p",{children:"在这个博客中，我会分享我的读书心得、思考感悟，以及个人品牌建设的实践经验。 希望这些内容能够对你有所帮助，也欢迎与我交流讨论。"})]})]})}),(0,a.jsxs)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-foreground mb-8 text-center",children:"我的兴趣"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:v.map((e,s)=>(0,a.jsx)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*s},viewport:{once:!0},children:(0,a.jsx)(p.Zp,{className:"h-full hover:shadow-lg transition-shadow",children:(0,a.jsx)(p.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"p-2 bg-primary/10 rounded-lg",children:(0,a.jsx)(e.icon,{className:"h-6 w-6 text-primary"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-foreground mb-2",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]})]})})})},e.name))})]}),(0,a.jsxs)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-foreground mb-8 text-center",children:"成长历程"}),(0,a.jsx)("div",{className:"space-y-6",children:w.map((e,s)=>(0,a.jsxs)(t.P.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5,delay:.1*s},viewport:{once:!0},className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-bold text-primary",children:e.year})}),(0,a.jsxs)("div",{className:"flex-1 pb-8",children:[(0,a.jsx)("h3",{className:"font-semibold text-foreground mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.description})]})]},e.year))})]}),(0,a.jsx)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:(0,a.jsx)(p.Zp,{className:"text-center",children:(0,a.jsxs)(p.Wu,{className:"p-8",children:[(0,a.jsx)(o.A,{className:"h-12 w-12 text-primary mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-4",children:"让我们保持联系"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"如果你对我的内容感兴趣，或者想要交流讨论，欢迎通过以下方式联系我"}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsx)(h.$,{asChild:!0,children:(0,a.jsxs)("a",{href:"mailto:<EMAIL>",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"发邮件"]})}),(0,a.jsx)(h.$,{variant:"outline",asChild:!0,children:(0,a.jsxs)("a",{href:"/rss.xml",target:"_blank",rel:"noopener noreferrer",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"订阅RSS"]})})]})]})})})]})})]})})}},9622:(e,s,i)=>{Promise.resolve().then(i.bind(i,3626))}},e=>{e.O(0,[458,707,788,441,964,358],()=>e(e.s=9622)),_N_E=e.O()}]);