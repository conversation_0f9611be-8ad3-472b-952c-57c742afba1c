"use strict";exports.id=874,exports.ids=[874],exports.modules={4780:(a,b,c)=>{c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},23991:(a,b,c)=>{c.d(b,{wj:()=>k,zX:()=>l,r:()=>m,f4:()=>o,QZ:()=>p,CI:()=>n});var d=c(81228);class e{set(a,b,c=3e5){this.cache.set(a,{data:b,timestamp:Date.now(),ttl:c})}get(a){let b=this.cache.get(a);return b?Date.now()-b.timestamp>b.ttl?(this.cache.delete(a),null):b.data:null}has(a){let b=this.cache.get(a);return!!b&&(!(Date.now()-b.timestamp>b.ttl)||(this.cache.delete(a),!1))}delete(a){return this.cache.delete(a)}clear(){this.cache.clear()}getStats(){let a=Date.now(),b=0,c=0;for(let[,d]of this.cache.entries())a-d.timestamp>d.ttl?c++:b++;return{total:this.cache.size,valid:b,expired:c}}cleanup(){let a=Date.now(),b=[];for(let[c,d]of this.cache.entries())a-d.timestamp>d.ttl&&b.push(c);b.forEach(a=>this.cache.delete(a))}constructor(){this.cache=new Map}}let f=new e,g={ALL_POSTS:"all_posts",POST_BY_NUMBER:a=>`post_${a}`,POSTS_BY_TAG:a=>`posts_tag_${a}`,ALL_TAGS:"all_tags"};function h(a,b,c=3e5){return async(...d)=>{let e=b(...d),g=f.get(e);if(null!==g)return g;let h=await a(...d);return f.set(e,h,c),h}}setInterval(()=>{f.cleanup()},6e5);var i=c(64152);let j=new d.E({auth:process.env.GITHUB_TOKEN}),k={owner:"hepingfly",repo:"hepingfly.github.io",title:"和平自留地",subtitle:"如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹",avatarUrl:"https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG",siteUrl:"https://hepingfly.github.io"},l=h(async function(){try{let{data:a}=await j.rest.issues.listForRepo({owner:k.owner,repo:k.repo,state:"open",sort:"created",direction:"desc",per_page:100});return a.filter(a=>!a.pull_request).map(a=>{let b=a.body||"";return{id:a.id,title:a.title,body:b,labels:a.labels.map(a=>"string"==typeof a?a:a.name||""),created_at:a.created_at,updated_at:a.updated_at,html_url:a.html_url,number:a.number,comments:a.comments,user:{login:a.user?.login||"",avatar_url:a.user?.avatar_url||""},excerpt:(0,i.dn)(b),readingTime:(0,i.qo)(b),slug:(0,i.z9)(a.title)}})}catch(a){throw console.error("Error fetching posts:",a),Error(`Failed to fetch posts: ${a instanceof Error?a.message:"Unknown error"}`)}},()=>g.ALL_POSTS,6e5);h(async function(a){try{let{data:b}=await j.rest.issues.get({owner:k.owner,repo:k.repo,issue_number:a});if(b.pull_request)return null;let c=b.body||"";return{id:b.id,title:b.title,body:c,labels:b.labels.map(a=>"string"==typeof a?a:a.name||""),created_at:b.created_at,updated_at:b.updated_at,html_url:b.html_url,number:b.number,comments:b.comments,user:{login:b.user?.login||"",avatar_url:b.user?.avatar_url||""},excerpt:(0,i.dn)(c),readingTime:(0,i.qo)(c),slug:(0,i.z9)(b.title)}}catch(b){if(console.error("Error fetching post:",b),b instanceof Error&&b.message.includes("404"))return null;throw Error(`Failed to fetch post ${a}: ${b instanceof Error?b.message:"Unknown error"}`)}},a=>g.POST_BY_NUMBER(a),9e5);let m=h(async function(){let a=await l(),b=new Set;return a.forEach(a=>{a.labels.forEach(a=>{a&&b.add(a)})}),Array.from(b).sort()},()=>g.ALL_TAGS,6e5);async function n(a){let b=await l(),c=a.toLowerCase();return b.filter(a=>{let b=a.title.toLowerCase().includes(c),d=a.body.toLowerCase().includes(c),e=a.labels.some(a=>a.toLowerCase().includes(c));return b||d||e})}async function o(a=5){return(await l()).slice(0,a)}async function p(){let a=await l(),b=new Map;return a.forEach(a=>{a.labels.forEach(a=>{a&&b.set(a,(b.get(a)||0)+1)})}),Array.from(b.entries()).map(([a,b])=>({name:a,count:b})).sort((a,b)=>b.count-a.count)}h(async function(a){return(await l()).filter(b=>b.labels.includes(a))},a=>g.POSTS_BY_TAG(a),6e5)},29523:(a,b,c)=>{c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(81391),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},32584:(a,b,c)=>{c.d(b,{BK:()=>h,eu:()=>g,q5:()=>i});var d=c(60687);c(43210);var e=c(96818),f=c(4780);function g({className:a,...b}){return(0,d.jsx)(e.bL,{"data-slot":"avatar",className:(0,f.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",a),...b})}function h({className:a,...b}){return(0,d.jsx)(e._V,{"data-slot":"avatar-image",className:(0,f.cn)("aspect-square size-full",a),...b})}function i({className:a,...b}){return(0,d.jsx)(e.H4,{"data-slot":"avatar-fallback",className:(0,f.cn)("bg-muted flex size-full items-center justify-center rounded-full",a),...b})}},35950:(a,b,c)=>{c.d(b,{w:()=>g});var d=c(60687);c(43210);var e=c(62369),f=c(4780);function g({className:a,orientation:b="horizontal",decorative:c=!0,...g}){return(0,d.jsx)(e.b,{"data-slot":"separator",decorative:c,orientation:b,className:(0,f.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...g})}},44493:(a,b,c)=>{c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},62901:(a,b,c)=>{c.d(b,{AC:()=>i,B$:()=>j,VP:()=>k,cU:()=>h,iV:()=>g});var d=c(43210),e=c(23991);function f(a,b=[]){let[c,e]=(0,d.useState)(null),[g,h]=(0,d.useState)(!0),[i,j]=(0,d.useState)(null);return{data:c,loading:g,error:i}}function g(){return f(e.zX)}function h(){return f(e.r)}function i(a=5){return f(()=>(0,e.f4)(a),[a])}function j(){return f(e.QZ)}function k(a="",b=300){var c;let[g,h]=(0,d.useState)(a),[i,j]=(0,d.useState)(a),l=f(()=>c?(0,e.CI)(c):Promise.resolve([]),[c=i]);return{query:g,setQuery:h,debouncedQuery:i,...l}}},64152:(a,b,c)=>{function d(a,b=200){let c=a.replace(/#{1,6}\s+/g,"").replace(/\*\*(.*?)\*\*/g,"$1").replace(/\*(.*?)\*/g,"$1").replace(/`(.*?)`/g,"$1").replace(/```[\s\S]*?```/g,"").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1").replace(/!\[([^\]]*)\]\([^)]+\)/g,"").replace(/\n+/g," ").trim();if(c.length<=b)return c;let e=c.substring(0,b),f=e.lastIndexOf(" ");return f>.8*b?e.substring(0,f)+"...":e+"..."}function e(a){let b=(a.match(/[\u4e00-\u9fff]/g)||[]).length;return Math.max(1,Math.round(b/300+a.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(a=>a.length>0).length/200))}function f(a){return a.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}function g(a,b="zh-CN"){let c=new Date(a);return"zh-CN"===b?c.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"}):c.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function h(a){let b=new Date(a),c=Math.floor((new Date().getTime()-b.getTime())/1e3);if(c<60)return"刚刚";let d=Math.floor(c/60);if(d<60)return`${d}分钟前`;let e=Math.floor(d/60);if(e<24)return`${e}小时前`;let f=Math.floor(e/24);if(f<30)return`${f}天前`;let g=Math.floor(f/30);if(g<12)return`${g}个月前`;let h=Math.floor(g/12);return`${h}年前`}c.d(b,{Yq:()=>g,dn:()=>d,fw:()=>h,qo:()=>e,z9:()=>f}),c(40121)},89667:(a,b,c)=>{c.d(b,{p:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},95874:(a,b,c)=>{c.d(b,{default:()=>U});var d=c(60687),e=c(43210),f=c(92576),g=c(85814),h=c.n(g),i=c(10218),j=c(88920),k=c(32192),l=c(82080),m=c(37360),n=c(58869),o=c(62157),p=c(48224),q=c(99270),r=c(21134),s=c(363),t=c(11860),u=c(12941),v=c(29523),w=c(32584),x=c(23991),y=c(70334),z=c(89667),A=c(96834),B=c(44493),C=c(62901);function D({isOpen:a,onClose:b}){let{query:c,setQuery:e,debouncedQuery:g,data:i,loading:k}=(0,C.VP)(),l=()=>{e(""),b()};return(0,d.jsx)(j.N,{children:a&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",onClick:l}),(0,d.jsx)(f.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"fixed top-0 left-0 right-0 z-50 bg-background border-b border-border",children:(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsxs)("div",{className:"flex-1 relative",children:[(0,d.jsx)(q.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(z.p,{placeholder:"搜索文章...",value:c,onChange:a=>e(a.target.value),className:"pl-10 pr-4",autoFocus:!0})]}),(0,d.jsx)(v.$,{variant:"ghost",size:"sm",onClick:l,children:(0,d.jsx)(t.A,{className:"h-4 w-4"})})]}),(0,d.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[k&&g&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"搜索中..."})]}),!k&&g&&i&&0===i.length&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(q.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"没有找到匹配的文章"})]}),!k&&i&&i.length>0&&(0,d.jsxs)("div",{className:"space-y-2",children:[i.slice(0,5).map(a=>(0,d.jsx)(h(),{href:`/post/${a.number}`,onClick:l,children:(0,d.jsx)(B.Zp,{className:"hover:bg-muted/50 transition-colors cursor-pointer",children:(0,d.jsx)(B.Wu,{className:"p-3",children:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h4",{className:"font-medium text-sm line-clamp-2",children:a.title}),a.excerpt&&(0,d.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-2",children:a.excerpt}),a.labels.length>0&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.labels.slice(0,2).map(a=>(0,d.jsx)(A.E,{variant:"outline",className:"text-xs",children:a},a)),a.labels.length>2&&(0,d.jsxs)(A.E,{variant:"outline",className:"text-xs",children:["+",a.labels.length-2]})]})]})})})},a.id)),i.length>5&&(0,d.jsx)(h(),{href:`/posts?q=${encodeURIComponent(g)}`,onClick:l,children:(0,d.jsx)(B.Zp,{className:"hover:bg-muted/50 transition-colors cursor-pointer",children:(0,d.jsx)(B.Wu,{className:"p-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm text-primary",children:[(0,d.jsxs)("span",{children:["查看所有 ",i.length," 个结果"]}),(0,d.jsx)(y.A,{className:"h-4 w-4"})]})})})})]}),!g&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(q.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"输入关键词开始搜索"})]})]})]})})]})})}let E=[{name:"首页",href:"/",icon:k.A},{name:"文章",href:"/posts",icon:l.A},{name:"标签",href:"/tags",icon:m.A},{name:"关于",href:"/about",icon:n.A}],F=[{name:"GitHub",href:"https://github.com/hepingfly",icon:o.A},{name:"RSS",href:"/rss.xml",icon:p.A}];function G(){let[a,b]=(0,e.useState)(!1),[c,g]=(0,e.useState)(!1),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(!1),{theme:o,setTheme:p}=(0,i.D)();return a?(0,d.jsxs)(f.P.header,{className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${m?"glass shadow-sm":"bg-transparent"}`,initial:{y:-100},animate:{y:0},transition:{duration:.3},children:[(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,d.jsx)(f.P.div,{className:"flex items-center space-x-3",whileHover:{scale:1.02},transition:{duration:.2},children:(0,d.jsxs)(h(),{href:"/",className:"flex items-center space-x-3",children:[(0,d.jsxs)(w.eu,{className:"h-8 w-8",children:[(0,d.jsx)(w.BK,{src:x.wj.avatarUrl,alt:x.wj.title}),(0,d.jsx)(w.q5,{children:"和"})]}),(0,d.jsxs)("div",{className:"hidden sm:block",children:[(0,d.jsx)("h1",{className:"text-lg font-bold text-foreground",children:x.wj.title}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground -mt-1",children:x.wj.subtitle})]})]})}),(0,d.jsx)("nav",{className:"hidden md:flex items-center space-x-1",children:E.map(a=>(0,d.jsx)(h(),{href:a.href,children:(0,d.jsxs)(v.$,{variant:"ghost",size:"sm",className:"flex items-center space-x-2 hover:bg-accent hover:text-accent-foreground",children:[(0,d.jsx)(a.icon,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.name})]})},a.name))}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(v.$,{variant:"ghost",size:"sm",onClick:()=>l(!0),children:(0,d.jsx)(q.A,{className:"h-4 w-4"})}),(0,d.jsx)("div",{className:"hidden sm:flex items-center space-x-1",children:F.map(a=>(0,d.jsx)(v.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,d.jsx)("a",{href:a.href,target:"_blank",rel:"noopener noreferrer",title:a.name,children:(0,d.jsx)(a.icon,{className:"h-4 w-4"})})},a.name))}),(0,d.jsx)(v.$,{variant:"ghost",size:"sm",onClick:()=>{p("dark"===o?"light":"dark")},title:"切换主题",children:"dark"===o?(0,d.jsx)(r.A,{className:"h-4 w-4"}):(0,d.jsx)(s.A,{className:"h-4 w-4"})}),(0,d.jsx)(v.$,{variant:"ghost",size:"sm",className:"md:hidden",onClick:()=>g(!c),children:c?(0,d.jsx)(t.A,{className:"h-4 w-4"}):(0,d.jsx)(u.A,{className:"h-4 w-4"})})]})]})}),(0,d.jsx)(j.N,{children:c&&(0,d.jsx)(f.P.div,{className:"md:hidden glass border-t border-border",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},children:(0,d.jsxs)("div",{className:"px-4 py-4 space-y-2",children:[E.map(a=>(0,d.jsx)(h(),{href:a.href,onClick:()=>g(!1),children:(0,d.jsxs)(v.$,{variant:"ghost",className:"w-full justify-start space-x-2",children:[(0,d.jsx)(a.icon,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.name})]})},a.name)),(0,d.jsx)("div",{className:"pt-2 border-t border-border",children:(0,d.jsx)("div",{className:"flex items-center space-x-2",children:F.map(a=>(0,d.jsx)(v.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,d.jsx)("a",{href:a.href,target:"_blank",rel:"noopener noreferrer",title:a.name,children:(0,d.jsx)(a.icon,{className:"h-4 w-4"})})},a.name))})})]})})}),(0,d.jsx)(D,{isOpen:k,onClose:()=>l(!1)})]}):null}var H=c(41550),I=c(67760),J=c(13166),K=c(2975),L=c(35950);let M={navigation:[{name:"首页",href:"/"},{name:"文章",href:"/posts"},{name:"标签",href:"/tags"},{name:"关于",href:"/about"}],social:[{name:"GitHub",href:"https://github.com/hepingfly",icon:o.A},{name:"RSS",href:"/rss.xml",icon:p.A},{name:"邮箱",href:"mailto:<EMAIL>",icon:H.A}],resources:[{name:"Gmeek",href:"https://github.com/Meekdai/Gmeek"},{name:"Next.js",href:"https://nextjs.org"},{name:"Tailwind CSS",href:"https://tailwindcss.com"},{name:"shadcn/ui",href:"https://ui.shadcn.com"}]};function N(){return(0,d.jsx)("footer",{className:"bg-background border-t border-border",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsx)("div",{className:"py-12",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,d.jsx)("div",{className:"lg:col-span-2",children:(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-4",children:x.wj.title}),(0,d.jsx)("p",{className:"text-muted-foreground mb-6 max-w-md",children:x.wj.subtitle}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:M.social.map(a=>(0,d.jsx)(v.$,{variant:"ghost",size:"sm",asChild:!0,className:"hover:text-primary",children:(0,d.jsx)("a",{href:a.href,target:"_blank",rel:"noopener noreferrer",title:a.name,children:(0,d.jsx)(a.icon,{className:"h-4 w-4"})})},a.name))})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},children:[(0,d.jsx)("h4",{className:"text-sm font-semibold text-foreground mb-4",children:"导航"}),(0,d.jsx)("ul",{className:"space-y-2",children:M.navigation.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(h(),{href:a.href,className:"text-sm text-muted-foreground hover:text-primary transition-colors",children:a.name})},a.name))})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},children:[(0,d.jsx)("h4",{className:"text-sm font-semibold text-foreground mb-4",children:"技术栈"}),(0,d.jsx)("ul",{className:"space-y-2",children:M.resources.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:a.href,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-muted-foreground hover:text-primary transition-colors",children:a.name})},a.name))})]})})]})}),(0,d.jsx)(L.w,{}),(0,d.jsx)("div",{className:"py-6",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0",children:[(0,d.jsxs)(f.P.div,{className:"flex items-center space-x-2 text-sm text-muted-foreground",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.5},viewport:{once:!0},children:[(0,d.jsxs)("span",{children:["\xa9 2024 ",x.wj.title]}),(0,d.jsx)("span",{children:"•"}),(0,d.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,d.jsx)("span",{children:"Made with"}),(0,d.jsx)(I.A,{className:"h-3 w-3 text-red-500 fill-current"}),(0,d.jsx)("span",{children:"and"}),(0,d.jsx)(J.A,{className:"h-3 w-3 text-amber-600"})]})]}),(0,d.jsxs)(f.P.div,{className:"flex items-center space-x-4",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.5,delay:.1},viewport:{once:!0},children:[(0,d.jsx)("span",{className:"text-sm text-muted-foreground",children:"转载请注明出处"}),(0,d.jsx)(v.$,{variant:"ghost",size:"sm",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"hover:text-primary",title:"回到顶部",children:(0,d.jsx)(K.A,{className:"h-4 w-4"})})]})]})})]})})}var O=c(16189),P=c(4780);let Q=[{name:"首页",href:"/",icon:k.A},{name:"文章",href:"/posts",icon:l.A},{name:"标签",href:"/tags",icon:m.A},{name:"关于",href:"/about",icon:n.A}];function R(){let a=(0,O.usePathname)();return(0,d.jsx)(f.P.nav,{initial:{y:100},animate:{y:0},className:"fixed bottom-0 left-0 right-0 z-40 bg-background/95 backdrop-blur-md border-t border-border safe-area-inset-bottom md:hidden",children:(0,d.jsx)("div",{className:"flex items-center justify-around px-4 py-2",children:Q.map(b=>{let c=a===b.href||"/"!==b.href&&a.startsWith(b.href);return(0,d.jsxs)(h(),{href:b.href,className:(0,P.cn)("flex flex-col items-center justify-center p-2 rounded-lg transition-colors relative",c?"text-primary":"text-muted-foreground hover:text-foreground"),children:[c&&(0,d.jsx)(f.P.div,{layoutId:"activeTab",className:"absolute inset-0 bg-primary/10 rounded-lg",transition:{type:"spring",duration:.4}}),(0,d.jsx)(b.icon,{className:(0,P.cn)("h-5 w-5 mb-1 relative z-10",c&&"scale-110")}),(0,d.jsx)("span",{className:"text-xs font-medium relative z-10",children:b.name})]},b.name)})})})}let S={initial:{opacity:0,y:20},in:{opacity:1,y:0},out:{opacity:0,y:-20}},T={type:"tween",ease:"anticipate",duration:.4};function U({children:a,className:b=""}){return(0,d.jsxs)("div",{className:"min-h-screen flex flex-col bg-background",children:[(0,d.jsx)(G,{}),(0,d.jsx)(f.P.main,{className:`flex-1 pt-16 pb-16 md:pb-0 ${b}`,initial:"initial",animate:"in",exit:"out",variants:S,transition:T,children:a}),(0,d.jsx)(N,{}),(0,d.jsx)(R,{})]})}},96834:(a,b,c)=>{c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(81391),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}}};