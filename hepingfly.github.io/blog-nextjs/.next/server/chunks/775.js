exports.id=775,exports.ids=[775],exports.modules={12127:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{resolveManifest:function(){return g},resolveRobots:function(){return e},resolveRouteData:function(){return h},resolveSitemap:function(){return f}});let d=c(77341);function e(a){let b="";for(let c of Array.isArray(a.rules)?a.rules:[a.rules]){for(let a of(0,d.resolveArray)(c.userAgent||["*"]))b+=`User-Agent: ${a}
`;if(c.allow)for(let a of(0,d.resolveArray)(c.allow))b+=`Allow: ${a}
`;if(c.disallow)for(let a of(0,d.resolveArray)(c.disallow))b+=`Disallow: ${a}
`;c.crawlDelay&&(b+=`Crawl-delay: ${c.crawlDelay}
`),b+="\n"}return a.host&&(b+=`Host: ${a.host}
`),a.sitemap&&(0,d.resolveArray)(a.sitemap).forEach(a=>{b+=`Sitemap: ${a}
`}),b}function f(a){let b=a.some(a=>Object.keys(a.alternates??{}).length>0),c=a.some(a=>{var b;return!!(null==(b=a.images)?void 0:b.length)}),d=a.some(a=>{var b;return!!(null==(b=a.videos)?void 0:b.length)}),e="";for(let i of(e+='<?xml version="1.0" encoding="UTF-8"?>\n',e+='<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',c&&(e+=' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"'),d&&(e+=' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"'),b?e+=' xmlns:xhtml="http://www.w3.org/1999/xhtml">\n':e+=">\n",a)){var f,g,h;e+="<url>\n",e+=`<loc>${i.url}</loc>
`;let a=null==(f=i.alternates)?void 0:f.languages;if(a&&Object.keys(a).length)for(let b in a)e+=`<xhtml:link rel="alternate" hreflang="${b}" href="${a[b]}" />
`;if(null==(g=i.images)?void 0:g.length)for(let a of i.images)e+=`<image:image>
<image:loc>${a}</image:loc>
</image:image>
`;if(null==(h=i.videos)?void 0:h.length)for(let a of i.videos)e+=["<video:video>",`<video:title>${a.title}</video:title>`,`<video:thumbnail_loc>${a.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${a.description}</video:description>`,a.content_loc&&`<video:content_loc>${a.content_loc}</video:content_loc>`,a.player_loc&&`<video:player_loc>${a.player_loc}</video:player_loc>`,a.duration&&`<video:duration>${a.duration}</video:duration>`,a.view_count&&`<video:view_count>${a.view_count}</video:view_count>`,a.tag&&`<video:tag>${a.tag}</video:tag>`,a.rating&&`<video:rating>${a.rating}</video:rating>`,a.expiration_date&&`<video:expiration_date>${a.expiration_date}</video:expiration_date>`,a.publication_date&&`<video:publication_date>${a.publication_date}</video:publication_date>`,a.family_friendly&&`<video:family_friendly>${a.family_friendly}</video:family_friendly>`,a.requires_subscription&&`<video:requires_subscription>${a.requires_subscription}</video:requires_subscription>`,a.live&&`<video:live>${a.live}</video:live>`,a.restriction&&`<video:restriction relationship="${a.restriction.relationship}">${a.restriction.content}</video:restriction>`,a.platform&&`<video:platform relationship="${a.platform.relationship}">${a.platform.content}</video:platform>`,a.uploader&&`<video:uploader${a.uploader.info&&` info="${a.uploader.info}"`}>${a.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(i.lastModified){let a=i.lastModified instanceof Date?i.lastModified.toISOString():i.lastModified;e+=`<lastmod>${a}</lastmod>
`}i.changeFrequency&&(e+=`<changefreq>${i.changeFrequency}</changefreq>
`),"number"==typeof i.priority&&(e+=`<priority>${i.priority}</priority>
`),e+="</url>\n"}return e+"</urlset>\n"}function g(a){return JSON.stringify(a)}function h(a,b){return"robots"===b?e(a):"sitemap"===b?f(a):"manifest"===b?g(a):""}},69345:(a,b,c)=>{"use strict";c.d(b,{wj:()=>m,zX:()=>n,r:()=>p,zl:()=>o,Pf:()=>q});var d=c(68373);class e{set(a,b,c=3e5){this.cache.set(a,{data:b,timestamp:Date.now(),ttl:c})}get(a){let b=this.cache.get(a);return b?Date.now()-b.timestamp>b.ttl?(this.cache.delete(a),null):b.data:null}has(a){let b=this.cache.get(a);return!!b&&(!(Date.now()-b.timestamp>b.ttl)||(this.cache.delete(a),!1))}delete(a){return this.cache.delete(a)}clear(){this.cache.clear()}getStats(){let a=Date.now(),b=0,c=0;for(let[,d]of this.cache.entries())a-d.timestamp>d.ttl?c++:b++;return{total:this.cache.size,valid:b,expired:c}}cleanup(){let a=Date.now(),b=[];for(let[c,d]of this.cache.entries())a-d.timestamp>d.ttl&&b.push(c);b.forEach(a=>this.cache.delete(a))}constructor(){this.cache=new Map}}let f=new e,g={ALL_POSTS:"all_posts",POST_BY_NUMBER:a=>`post_${a}`,POSTS_BY_TAG:a=>`posts_tag_${a}`,ALL_TAGS:"all_tags"};function h(a,b,c=3e5){return async(...d)=>{let e=b(...d),g=f.get(e);if(null!==g)return g;let h=await a(...d);return f.set(e,h,c),h}}function i(a,b=200){let c=a.replace(/#{1,6}\s+/g,"").replace(/\*\*(.*?)\*\*/g,"$1").replace(/\*(.*?)\*/g,"$1").replace(/`(.*?)`/g,"$1").replace(/```[\s\S]*?```/g,"").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1").replace(/!\[([^\]]*)\]\([^)]+\)/g,"").replace(/\n+/g," ").trim();if(c.length<=b)return c;let d=c.substring(0,b),e=d.lastIndexOf(" ");return e>.8*b?d.substring(0,e)+"...":d+"..."}function j(a){let b=(a.match(/[\u4e00-\u9fff]/g)||[]).length;return Math.max(1,Math.round(b/300+a.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(a=>a.length>0).length/200))}function k(a){return a.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}setInterval(()=>{f.cleanup()},6e5),c(99379);let l=new d.E({auth:process.env.GITHUB_TOKEN}),m={owner:"hepingfly",repo:"hepingfly.github.io",title:"和平自留地",subtitle:"如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹",avatarUrl:"https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG",siteUrl:"https://hepingfly.github.io"},n=h(async function(){try{let{data:a}=await l.rest.issues.listForRepo({owner:m.owner,repo:m.repo,state:"open",sort:"created",direction:"desc",per_page:100});return a.filter(a=>!a.pull_request).map(a=>{let b=a.body||"";return{id:a.id,title:a.title,body:b,labels:a.labels.map(a=>"string"==typeof a?a:a.name||""),created_at:a.created_at,updated_at:a.updated_at,html_url:a.html_url,number:a.number,comments:a.comments,user:{login:a.user?.login||"",avatar_url:a.user?.avatar_url||""},excerpt:i(b),readingTime:j(b),slug:k(a.title)}})}catch(a){throw console.error("Error fetching posts:",a),Error(`Failed to fetch posts: ${a instanceof Error?a.message:"Unknown error"}`)}},()=>g.ALL_POSTS,6e5),o=h(async function(a){try{let{data:b}=await l.rest.issues.get({owner:m.owner,repo:m.repo,issue_number:a});if(b.pull_request)return null;let c=b.body||"";return{id:b.id,title:b.title,body:c,labels:b.labels.map(a=>"string"==typeof a?a:a.name||""),created_at:b.created_at,updated_at:b.updated_at,html_url:b.html_url,number:b.number,comments:b.comments,user:{login:b.user?.login||"",avatar_url:b.user?.avatar_url||""},excerpt:i(c),readingTime:j(c),slug:k(b.title)}}catch(b){if(console.error("Error fetching post:",b),b instanceof Error&&b.message.includes("404"))return null;throw Error(`Failed to fetch post ${a}: ${b instanceof Error?b.message:"Unknown error"}`)}},a=>g.POST_BY_NUMBER(a),9e5),p=h(async function(){let a=await n(),b=new Set;return a.forEach(a=>{a.labels.forEach(a=>{a&&b.add(a)})}),Array.from(b).sort()},()=>g.ALL_TAGS,6e5),q=h(async function(a){return(await n()).filter(b=>b.labels.includes(a))},a=>g.POSTS_BY_TAG(a),6e5)},77341:(a,b)=>{"use strict";function c(a){return Array.isArray(a)?a:[a]}function d(a){if(null!=a)return c(a)}function e(a){let b;if("string"==typeof a)try{b=(a=new URL(a)).origin}catch{}return b}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getOrigin:function(){return e},resolveArray:function(){return c},resolveAsArrayOrUndefined:function(){return d}})},78335:()=>{},96487:()=>{}};