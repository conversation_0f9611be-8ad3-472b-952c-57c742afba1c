exports.id=44,exports.ids=[44],exports.modules={4514:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},8061:(a,b,c)=>{Promise.resolve().then(c.bind(c,10218)),Promise.resolve().then(c.bind(c,23071))},12797:(a,b,c)=>{Promise.resolve().then(c.bind(c,23392)),Promise.resolve().then(c.bind(c,73021))},22666:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},23071:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687);c(43210);var e=c(72600);let f="G-PB7Y2QXTLR";function g(){return f?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(e.default,{strategy:"afterInteractive",src:`https://www.googletagmanager.com/gtag/js?id=${f}`}),(0,d.jsx)(e.default,{id:"google-analytics",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${f}', {
              page_path: window.location.pathname,
              anonymize_ip: true,
              allow_google_signals: false,
              allow_ad_personalization_signals: false,
            });
          `}})]}):null}},61135:()=>{},69345:(a,b,c)=>{"use strict";c.d(b,{wj:()=>m,zX:()=>n,r:()=>p,zl:()=>o,Pf:()=>q});var d=c(68373);class e{set(a,b,c=3e5){this.cache.set(a,{data:b,timestamp:Date.now(),ttl:c})}get(a){let b=this.cache.get(a);return b?Date.now()-b.timestamp>b.ttl?(this.cache.delete(a),null):b.data:null}has(a){let b=this.cache.get(a);return!!b&&(!(Date.now()-b.timestamp>b.ttl)||(this.cache.delete(a),!1))}delete(a){return this.cache.delete(a)}clear(){this.cache.clear()}getStats(){let a=Date.now(),b=0,c=0;for(let[,d]of this.cache.entries())a-d.timestamp>d.ttl?c++:b++;return{total:this.cache.size,valid:b,expired:c}}cleanup(){let a=Date.now(),b=[];for(let[c,d]of this.cache.entries())a-d.timestamp>d.ttl&&b.push(c);b.forEach(a=>this.cache.delete(a))}constructor(){this.cache=new Map}}let f=new e,g={ALL_POSTS:"all_posts",POST_BY_NUMBER:a=>`post_${a}`,POSTS_BY_TAG:a=>`posts_tag_${a}`,ALL_TAGS:"all_tags"};function h(a,b,c=3e5){return async(...d)=>{let e=b(...d),g=f.get(e);if(null!==g)return g;let h=await a(...d);return f.set(e,h,c),h}}function i(a,b=200){let c=a.replace(/#{1,6}\s+/g,"").replace(/\*\*(.*?)\*\*/g,"$1").replace(/\*(.*?)\*/g,"$1").replace(/`(.*?)`/g,"$1").replace(/```[\s\S]*?```/g,"").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1").replace(/!\[([^\]]*)\]\([^)]+\)/g,"").replace(/\n+/g," ").trim();if(c.length<=b)return c;let d=c.substring(0,b),e=d.lastIndexOf(" ");return e>.8*b?d.substring(0,e)+"...":d+"..."}function j(a){let b=(a.match(/[\u4e00-\u9fff]/g)||[]).length;return Math.max(1,Math.round(b/300+a.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(a=>a.length>0).length/200))}function k(a){return a.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}setInterval(()=>{f.cleanup()},6e5),c(99379);let l=new d.E({auth:process.env.GITHUB_TOKEN}),m={owner:"hepingfly",repo:"hepingfly.github.io",title:"和平自留地",subtitle:"如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹",avatarUrl:"https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG",siteUrl:"https://hepingfly.github.io"},n=h(async function(){try{let{data:a}=await l.rest.issues.listForRepo({owner:m.owner,repo:m.repo,state:"open",sort:"created",direction:"desc",per_page:100});return a.filter(a=>!a.pull_request).map(a=>{let b=a.body||"";return{id:a.id,title:a.title,body:b,labels:a.labels.map(a=>"string"==typeof a?a:a.name||""),created_at:a.created_at,updated_at:a.updated_at,html_url:a.html_url,number:a.number,comments:a.comments,user:{login:a.user?.login||"",avatar_url:a.user?.avatar_url||""},excerpt:i(b),readingTime:j(b),slug:k(a.title)}})}catch(a){throw console.error("Error fetching posts:",a),Error(`Failed to fetch posts: ${a instanceof Error?a.message:"Unknown error"}`)}},()=>g.ALL_POSTS,6e5),o=h(async function(a){try{let{data:b}=await l.rest.issues.get({owner:m.owner,repo:m.repo,issue_number:a});if(b.pull_request)return null;let c=b.body||"";return{id:b.id,title:b.title,body:c,labels:b.labels.map(a=>"string"==typeof a?a:a.name||""),created_at:b.created_at,updated_at:b.updated_at,html_url:b.html_url,number:b.number,comments:b.comments,user:{login:b.user?.login||"",avatar_url:b.user?.avatar_url||""},excerpt:i(c),readingTime:j(c),slug:k(b.title)}}catch(b){if(console.error("Error fetching post:",b),b instanceof Error&&b.message.includes("404"))return null;throw Error(`Failed to fetch post ${a}: ${b instanceof Error?b.message:"Unknown error"}`)}},a=>g.POST_BY_NUMBER(a),9e5),p=h(async function(){let a=await n(),b=new Set;return a.forEach(a=>{a.labels.forEach(a=>{a&&b.add(a)})}),Array.from(b).sort()},()=>g.ALL_TAGS,6e5),q=h(async function(a){return(await n()).filter(b=>b.labels.includes(a))},a=>g.POSTS_BY_TAG(a),6e5)},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},73021:(a,b,c)=>{"use strict";c.d(b,{default:()=>e});var d=c(61369);(0,d.registerClientReference)(function(){throw Error("Attempted to call pageview() from the server but pageview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/analytics/Analytics.tsx","pageview"),(0,d.registerClientReference)(function(){throw Error("Attempted to call event() from the server but event is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/analytics/Analytics.tsx","event"),(0,d.registerClientReference)(function(){throw Error("Attempted to call trackWebVitals() from the server but trackWebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/analytics/Analytics.tsx","trackWebVitals");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/analytics/Analytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/analytics/Analytics.tsx","default")},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>l});var d=c(37413),e=c(31001),f=c.n(e),g=c(92092),h=c.n(g),i=c(23392);c(61135);var j=c(69345),k=c(73021);let l={title:{default:j.wj.title,template:`%s | ${j.wj.title}`},description:j.wj.subtitle,keywords:["个人博客","个人IP","读书分享","思维成长","Next.js"],authors:[{name:"和平",url:j.wj.siteUrl}],creator:"和平",openGraph:{type:"website",locale:"zh_CN",url:j.wj.siteUrl,title:j.wj.title,description:j.wj.subtitle,siteName:j.wj.title,images:[{url:j.wj.avatarUrl,width:1200,height:630,alt:j.wj.title}]},twitter:{card:"summary_large_image",title:j.wj.title,description:j.wj.subtitle,images:[j.wj.avatarUrl]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function m({children:a}){return(0,d.jsx)("html",{lang:"zh-CN",suppressHydrationWarning:!0,children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} font-sans antialiased`,children:(0,d.jsxs)(i.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[a,(0,d.jsx)(k.default,{})]})})})}}};