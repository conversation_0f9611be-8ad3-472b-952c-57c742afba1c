1:"$Sreact.fragment"
2:I[1362,["177","static/chunks/app/layout-c1633986ddcf7869.js"],"ThemeProvider"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[6453,["177","static/chunks/app/layout-c1633986ddcf7869.js"],"default"]
7:I[9665,[],"OutletBoundary"]
9:I[4911,[],"AsyncMetadataOutlet"]
b:I[9665,[],"ViewportBoundary"]
d:I[9665,[],"MetadataBoundary"]
e:"$Sreact.suspense"
10:I[8393,[],""]
:HL["/_next/static/media/bb3ef058b751a6ad-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/e4af272ccee01ff0-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/e7c402068c19d32e.css","style"]
0:{"P":null,"b":"J6PK5-nEpwEaPIFt4i3Gz","p":"","c":["","post","2",""],"i":false,"f":[[["",{"children":["post",{"children":[["number","2","d"],{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e7c402068c19d32e.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"zh-CN","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_e8ce0c __variable_3c557b font-sans antialiased","children":["$","$L2",null,{"attribute":"class","defaultTheme":"system","enableSystem":true,"disableTransitionOnChange":true,"children":[["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}],["$","$L5",null,{}]]}]}]}]]}],{"children":["post",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":[["number","2","d"],["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":["$L6",null,["$","$L7",null,{"children":["$L8",["$","$L9",null,{"promise":"$@a"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Lb",null,{"children":"$Lc"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$Ld",null,{"children":["$","div",null,{"hidden":true,"children":["$","$e",null,{"fallback":null,"children":"$Lf"}]}]}]]}],false]],"m":"$undefined","G":["$10",[]],"s":false,"S":true}
c:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
8:null
11:I[6705,["458","static/chunks/e58627ac-a79280295dcf1433.js","707","static/chunks/707-81b7d3c46238013e.js","788","static/chunks/788-6e98c5cada96323d.js","729","static/chunks/app/post/%5Bnumber%5D/page-ec2f9ee14f1143da.js"],"default"]
13:I[8175,[],"IconMark"]
12:T17d0,想要做成一个 IP，除了要有真人出镜的表现力之外，你还需要懂人群，懂需求，你是能够真正帮助这部分人群去解决他们的问题的。

<img src="https://cdn.jsdelivr.net/gh/hepingfly/images@main/%E7%B4%A0%E4%BA%BAip%E9%93%BE.png" style="zoom:80%;" />

做 IP 首先要思考的几件事：

1、自己的目标人群是什么？这部分人群画像是什么？

补充：年龄只是人群画像其中一个维度，不能一说到目标人群就是 XXX 岁，还有其他维度比如很重视健康的人群、很焦虑的人群、有/无孩子的人群

2、这部分人（你的目标人群）的需求是什么？

想明白这两件事之后，我们要做的就是根据上面这两个问题给出解决方案。解决方案可以是产品也可以是服务。

有了解决方案之后，我们要把这个解决方案给呈现出来，呈现到用户面前去吸引用户，然后去做转化。

**注：**

> 做IP 一定要围绕目标人群去做。
>
> 比如你的人群是中老年人群，那你发的内容就不能一会吸引中老年群体，一会又去吸引宝妈群体，这样系统无法辨认，无法给你准确推流。

以「国之脊梁」这本书为例，来分析一下这个逻辑：

比如我们现在发现很多爆款视频，都在带《国之脊梁》这本书。当我刷到这个爆款视频，假设我是做历史赛道的，那么现在问题来了，这个爆款视频还有这个品我能不能跟？

> 回答这个问题，首先你需要思考：你面向的是什么人群？你解决的是什么需求？
>
> 只有这个爆款视频面向的人群和你的目标人群一致，解决的需求和你的账号属性匹配，这时候才适合你去跟。
>
> 所以我们可以去挖《国之脊梁》这本书它的爆款文案，**解决了什么需求？**（自己判断）还有**面向的人群**是什么？（可以用巨量百应去看）
>
> 人群：宝妈，家里有小孩子的
>
> 需求：暑假到了，和孩子一起看一本书（育儿）
>
> 解决方案：
>
> 找一些榜样，给孩子树立榜样。所以宝妈在教育孩子的时候就有了谈资，教育孩子的谈资。告诉你一些榜样的故事，宝妈可以拿着这些故事去教育孩子。
>
> 呈现形式：混剪名人视频、口播
>
> 转化：告诉你我上面说的这些榜样故事，在这本书里面都有，你把这本书买回去，你就可以和你的孩子一起看，来教育孩子。

**思考：**

围绕这一整个 IP 链，有哪些地方可以调整？

==改变解决方案==

例1：

我就针对当前一样的人群，一样的需求，去一直做不同的解决方案。

比如围绕宝妈需要教育孩子，陪着孩子一起看书这个需求，爆款视频是一起看《国之脊梁》这本书，现在我给出不同的解决方案，这时候我就可以拓宽品类了，去带一些《漫画版王阳明》《规矩》等等品。

接下来就去摸清楚，用什么样的呈现形式把这些解决方案给呈现出来。（呈现形式需要不断迭代...）

`慢慢做着做着就会让用户形成一种认知，我想要找育儿的解决方法，那么我直接来找这个账号就好了，至此 IP 就成了`。

例2：

针对当前一样的人群，一样的需求，同一个解决方案（但是围绕这个解决方案去做不同的内容）

比如上面《国之脊梁》这本书，他用的是 AB 两个榜样，那我就可以换成 CD 两个榜样用现有的爆款文案框架再做一遍，换汤不换药。

**注：**

这个解决方案，如果你有商品通过商品去帮用户解决，那么你就带货。如果没有商品，那么你给出的解决方案就用来积累信任，积累影响力，积累账号权重。

==改变呈现方式==

例：

我就针对当前一样的人群，一样的需求，一样的解决方案，去做内容形式的改变

a.爆款视频采用的是混剪方式，那么我们就可以把它改成真人出镜口播的方式

b.别人用真人口播，那么我真人口播 + 黄金 3 秒吸睛 + 服化道到位

c.别人真人口播 + 黄金 3 秒吸睛 + 服化道到位，那么我找一个年纪大一点的老外来出镜口播



> a.做 IP 过程中有一个问题是始终要思考的，就是用户为什么要关注你？
>
> 
>
> b.每一个爆品（可以在抖音巨量百应看到）背后都有对应的人群和需求
>
> 我们就可以去挖掘这个爆品背后的需求
>
> c.如果想要做一个有生命力的 IP ，是确确实实需要去研究这个人群，去懂这个人群的。
>
> 你如果有几千个信任你这个 IP 的人，那么一年变现几十万是不成问题的。



案例：

比如现在的目标人群是一群非常内耗的人群。那么这部分人一定有一个需求就是，解决内耗。那么我针对这部分内耗人群，我应该怎么样去做**流量型内容**呢？

我可以在抖音去搜索「内耗」这个关键词：

a.结果会出来很多的高赞爆款视频。从这些爆款视频中我就可以知道：

1、「内耗人群」会被什么样的文案（内容/内容形式）吸引。

2、从这些爆款文案/评论区中我们可以知道用户的痛点

b.结果会出来一些非高赞爆款视频，一些很普通的视频也会出来。其中有些视频会有一个特点，就是它确实给出了解决方案，告诉你如何去解决内耗这个问题。

所以就衍生出来做 IP 的两种思路：

1）一种就是类似于「一个人的莎士比亚」，用户喜欢什么我就说什么。

2）实实在在给出解决方案的。比如我也是一个内耗的人，我是通过什么方式去解决内耗的。把这个方法分享给用户，真正帮你去解决你的内耗。

所以我们可以通过这种方式去理解你的用户，理解你用户有哪些痛点？理解你用户的痛点需要怎么去解决？去找同行进行学习。6:["$","$L11",null,{"post":{"id":2406000881,"title":"个人IP底层逻辑","body":"$12","labels":["个人IP"],"created_at":"2024-07-12T17:02:20Z","updated_at":"2024-07-12T17:02:20Z","html_url":"https://github.com/hepingfly/hepingfly.github.io/issues/2","number":2,"comments":0,"user":{"login":"hepingfly","avatar_url":"https://avatars.githubusercontent.com/u/18638517?v=4"},"excerpt":"想要做成一个 IP，除了要有真人出镜的表现力之外，你还需要懂人群，懂需求，你是能够真正帮助这部分人群去解决他们的问题的。\r \r <img src=\"https://cdn.jsdelivr.net/gh/hepingfly/images@main/%E7%B4%A0%E4%BA%BAip%E9%93%BE.png\" style=\"zoom:80%;\" />\r \r 做 IP 首先要思考的几件事：\r...","readingTime":6,"slug":"ip"}}]
a:{"metadata":[["$","title","0",{"children":"个人IP底层逻辑 | 和平自留地 | 和平自留地"}],["$","meta","1",{"name":"description","content":"想要做成一个 IP，除了要有真人出镜的表现力之外，你还需要懂人群，懂需求，你是能够真正帮助这部分人群去解决他们的问题的。\r \r <img src=\"https://cdn.jsdelivr.net/gh/hepingfly/images@main/%E7%B4%A0%E4%BA%BAip%E9%93%BE.png\" style=\"zoom:80%;\" />\r \r 做 IP 首先要思考的几件事：\r..."}],["$","link","2",{"rel":"author","href":"https://hepingfly.github.io"}],["$","meta","3",{"name":"author","content":"和平"}],["$","meta","4",{"name":"keywords","content":"个人博客, 个人IP, 读书分享, 思维成长, Next.js, 和平自留地, 个人IP, 个人IP"}],["$","meta","5",{"name":"creator","content":"和平"}],["$","meta","6",{"name":"publisher","content":"和平"}],["$","meta","7",{"name":"robots","content":"index, follow"}],["$","meta","8",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","9",{"name":"category","content":"个人博客"}],["$","meta","10",{"name":"google-site-verification","content":"your-google-verification-code"}],["$","meta","11",{"property":"og:title","content":"个人IP底层逻辑 | 和平自留地"}],["$","meta","12",{"property":"og:description","content":"想要做成一个 IP，除了要有真人出镜的表现力之外，你还需要懂人群，懂需求，你是能够真正帮助这部分人群去解决他们的问题的。\r \r <img src=\"https://cdn.jsdelivr.net/gh/hepingfly/images@main/%E7%B4%A0%E4%BA%BAip%E9%93%BE.png\" style=\"zoom:80%;\" />\r \r 做 IP 首先要思考的几件事：\r..."}],["$","meta","13",{"property":"og:url","content":"https://hepingfly.github.io/post/2/"}],["$","meta","14",{"property":"og:site_name","content":"和平自留地"}],["$","meta","15",{"property":"og:locale","content":"zh_CN"}],["$","meta","16",{"property":"og:image","content":"https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG"}],["$","meta","17",{"property":"og:image:width","content":"1200"}],["$","meta","18",{"property":"og:image:height","content":"630"}],["$","meta","19",{"property":"og:image:alt","content":"个人IP底层逻辑"}],["$","meta","20",{"property":"og:type","content":"article"}],["$","meta","21",{"property":"article:published_time","content":"2024-07-12T17:02:20Z"}],["$","meta","22",{"property":"article:modified_time","content":"2024-07-12T17:02:20Z"}],["$","meta","23",{"property":"article:author","content":"和平"}],["$","meta","24",{"property":"article:tag","content":"个人IP"}],["$","meta","25",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","26",{"name":"twitter:creator","content":"@hepingfly"}],["$","meta","27",{"name":"twitter:title","content":"个人IP底层逻辑 | 和平自留地"}],["$","meta","28",{"name":"twitter:description","content":"想要做成一个 IP，除了要有真人出镜的表现力之外，你还需要懂人群，懂需求，你是能够真正帮助这部分人群去解决他们的问题的。\r \r <img src=\"https://cdn.jsdelivr.net/gh/hepingfly/images@main/%E7%B4%A0%E4%BA%BAip%E9%93%BE.png\" style=\"zoom:80%;\" />\r \r 做 IP 首先要思考的几件事：\r..."}],["$","meta","29",{"name":"twitter:image","content":"https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG"}],["$","link","30",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L13","31",{}]],"error":null,"digest":"$undefined"}
f:"$a:metadata"
