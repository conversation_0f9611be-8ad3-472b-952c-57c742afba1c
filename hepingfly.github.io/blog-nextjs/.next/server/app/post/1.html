<!DOCTYPE html><!--J6PK5_nEpwEaPIFt4i3Gz--><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/bb3ef058b751a6ad-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/e7c402068c19d32e.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-6e2a6b61b0a26b22.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-9b249bc0ac5a9a8b.js" async=""></script><script src="/_next/static/chunks/main-app-dbe24eb460dffff3.js" async=""></script><script src="/_next/static/chunks/app/layout-c1633986ddcf7869.js" async=""></script><script src="/_next/static/chunks/e58627ac-a79280295dcf1433.js" async=""></script><script src="/_next/static/chunks/707-81b7d3c46238013e.js" async=""></script><script src="/_next/static/chunks/788-6e98c5cada96323d.js" async=""></script><script src="/_next/static/chunks/app/post/%5Bnumber%5D/page-ec2f9ee14f1143da.js" async=""></script><link rel="preload" href="https://www.googletagmanager.com/gtag/js?id=G-PB7Y2QXTLR" as="script"/><meta name="next-size-adjust" content=""/><title>第一篇博客测试 | 和平自留地 | 和平自留地</title><meta name="description" content="测试能不能成功22"/><link rel="author" href="https://hepingfly.github.io"/><meta name="author" content="和平"/><meta name="keywords" content="个人博客, 个人IP, 读书分享, 思维成长, Next.js, 和平自留地"/><meta name="creator" content="和平"/><meta name="publisher" content="和平"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="category" content="个人博客"/><meta name="google-site-verification" content="your-google-verification-code"/><meta property="og:title" content="第一篇博客测试 | 和平自留地"/><meta property="og:description" content="测试能不能成功22"/><meta property="og:url" content="https://hepingfly.github.io/post/1/"/><meta property="og:site_name" content="和平自留地"/><meta property="og:locale" content="zh_CN"/><meta property="og:image" content="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="第一篇博客测试"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2024-06-24T08:19:49Z"/><meta property="article:modified_time" content="2024-07-16T16:51:24Z"/><meta property="article:author" content="和平"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@hepingfly"/><meta name="twitter:title" content="第一篇博客测试 | 和平自留地"/><meta name="twitter:description" content="测试能不能成功22"/><meta name="twitter:image" content="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_e8ce0c __variable_3c557b font-sans antialiased"><div hidden=""><!--$--><!--/$--></div><script>((a,b,c,d,e,f,g,h)=>{let i=document.documentElement,j=["light","dark"];function k(b){var c;(Array.isArray(a)?a:[a]).forEach(a=>{let c="class"===a,d=c&&f?e.map(a=>f[a]||a):e;c?(i.classList.remove(...d),i.classList.add(f&&f[b]?f[b]:b)):i.setAttribute(a,b)}),c=b,h&&j.includes(c)&&(i.style.colorScheme=c)}if(d)k(d);else try{let a=localStorage.getItem(b)||c,d=g&&"system"===a?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":a;k(d)}catch(a){}})("class","theme","system",null,["light","dark"],null,true,true)</script><div class="min-h-screen flex flex-col bg-background"><main class="flex-1 pt-16 pb-16 md:pb-0 " style="opacity:0;transform:translateY(20px)"><article class="min-h-screen bg-background"><section class="py-8 bg-muted/30"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="space-y-6" style="opacity:0;transform:translateY(20px)"><a data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-9 px-4 py-2 has-[&gt;svg]:px-3 group" href="/posts/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" aria-hidden="true"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>返回文章列表</a><div class="space-y-6"><h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground leading-tight">第一篇博客测试</h1><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"><div class="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-6 text-sm text-muted-foreground"><div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg><span>2024年6月24日</span></div><div class="flex items-center space-x-4"><div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-4 w-4" aria-hidden="true"><path d="M12 6v6l4 2"></path><circle cx="12" cy="12" r="10"></circle></svg><span>1<!-- --> 分钟阅读</span></div><div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle h-4 w-4" aria-hidden="true"><path d="M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719"></path></svg><span>1<!-- --> 条评论</span></div></div></div><div class="flex items-center space-x-2"><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 group"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-share2 lucide-share-2 h-4 w-4 sm:mr-2 group-hover:scale-110 transition-transform" aria-hidden="true"><circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line><line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line></svg><span class="hidden sm:inline">分享</span></button><a href="https://github.com/hepingfly/hepingfly.github.io/issues/1" target="_blank" rel="noopener noreferrer" data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github h-4 w-4 sm:mr-2" aria-hidden="true"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg><span class="hidden sm:inline">GitHub</span></a></div></div></div></div></div></section><section class="py-12"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="grid grid-cols-1 lg:grid-cols-4 gap-8"><div class="lg:col-span-3 order-2 lg:order-1" style="opacity:0;transform:translateY(20px)"><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm prose prose-sm sm:prose-lg max-w-none dark:prose-invert"><div data-slot="card-content" class="p-4 sm:p-6 lg:p-8"><div class="markdown-content">测试能不能成功22</div></div></div><div class="mt-8 pt-8 border-t border-border"><div class="flex items-center justify-between"><div class="text-sm text-muted-foreground">最后更新于 <!-- -->1年前</div><div class="flex items-center space-x-4"><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 group"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart h-4 w-4 mr-2 group-hover:text-red-500 transition-colors" aria-hidden="true"><path d="M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5"></path></svg>喜欢这篇文章</button></div></div></div></div><div class="lg:col-span-1 order-1 lg:order-2" style="opacity:0;transform:translateX(20px)"><div class="lg:sticky lg:top-24 space-y-6"><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm"><div data-slot="card-content" class="p-6 text-center"><span data-slot="avatar" class="relative flex size-8 shrink-0 overflow-hidden rounded-full h-16 w-16 mx-auto mb-4"><span data-slot="avatar-fallback" class="bg-muted flex size-full items-center justify-center rounded-full">和</span></span><h3 class="font-semibold text-foreground mb-2">和平</h3><p class="text-sm text-muted-foreground mb-4">个人品牌建设者 · 终身学习者</p><a data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 w-full" href="/about/">了解更多</a></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm"><div data-slot="card-content" class="p-6"><h4 class="font-semibold text-foreground mb-4">文章信息</h4><div class="space-y-3 text-sm"><div class="flex items-center justify-between"><span class="text-muted-foreground">发布时间</span><span class="text-foreground">2024年6月24日</span></div><div class="flex items-center justify-between"><span class="text-muted-foreground">阅读时间</span><span class="text-foreground">1<!-- --> 分钟</span></div><div class="flex items-center justify-between"><span class="text-muted-foreground">字数统计</span><span class="text-foreground">9<!-- --> 字符</span></div><div class="flex items-center justify-between"><span class="text-muted-foreground">评论数</span><span class="text-foreground">1</span></div></div></div></div></div></div></div></div></section></article></main><footer class="bg-background border-t border-border"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="py-12"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"><div class="lg:col-span-2"><div style="opacity:0;transform:translateY(20px)"><h3 class="text-lg font-semibold text-foreground mb-4">和平自留地</h3><p class="text-muted-foreground mb-6 max-w-md">如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹</p><div class="flex items-center space-x-4"><a href="https://github.com/hepingfly" target="_blank" rel="noopener noreferrer" title="GitHub" data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 hover:text-primary"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github h-4 w-4" aria-hidden="true"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></a><a href="/rss.xml" target="_blank" rel="noopener noreferrer" title="RSS" data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 hover:text-primary"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rss h-4 w-4" aria-hidden="true"><path d="M4 11a9 9 0 0 1 9 9"></path><path d="M4 4a16 16 0 0 1 16 16"></path><circle cx="5" cy="19" r="1"></circle></svg></a><a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer" title="邮箱" data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 hover:text-primary"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-4 w-4" aria-hidden="true"><path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"></path><rect x="2" y="4" width="20" height="16" rx="2"></rect></svg></a></div></div></div><div><div style="opacity:0;transform:translateY(20px)"><h4 class="text-sm font-semibold text-foreground mb-4">导航</h4><ul class="space-y-2"><li><a class="text-sm text-muted-foreground hover:text-primary transition-colors" href="/">首页</a></li><li><a class="text-sm text-muted-foreground hover:text-primary transition-colors" href="/posts/">文章</a></li><li><a class="text-sm text-muted-foreground hover:text-primary transition-colors" href="/tags/">标签</a></li><li><a class="text-sm text-muted-foreground hover:text-primary transition-colors" href="/about/">关于</a></li></ul></div></div><div><div style="opacity:0;transform:translateY(20px)"><h4 class="text-sm font-semibold text-foreground mb-4">技术栈</h4><ul class="space-y-2"><li><a href="https://github.com/Meekdai/Gmeek" target="_blank" rel="noopener noreferrer" class="text-sm text-muted-foreground hover:text-primary transition-colors">Gmeek</a></li><li><a href="https://nextjs.org" target="_blank" rel="noopener noreferrer" class="text-sm text-muted-foreground hover:text-primary transition-colors">Next.js</a></li><li><a href="https://tailwindcss.com" target="_blank" rel="noopener noreferrer" class="text-sm text-muted-foreground hover:text-primary transition-colors">Tailwind CSS</a></li><li><a href="https://ui.shadcn.com" target="_blank" rel="noopener noreferrer" class="text-sm text-muted-foreground hover:text-primary transition-colors">shadcn/ui</a></li></ul></div></div></div></div><div data-orientation="horizontal" role="none" data-slot="separator" class="bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px"></div><div class="py-6"><div class="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0"><div class="flex items-center space-x-2 text-sm text-muted-foreground" style="opacity:0"><span>© 2024 <!-- -->和平自留地</span><span>•</span><span class="flex items-center space-x-1"><span>Made with</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart h-3 w-3 text-red-500 fill-current" aria-hidden="true"><path d="M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5"></path></svg><span>and</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-coffee h-3 w-3 text-amber-600" aria-hidden="true"><path d="M10 2v2"></path><path d="M14 2v2"></path><path d="M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1"></path><path d="M6 2v2"></path></svg></span></div><div class="flex items-center space-x-4" style="opacity:0"><span class="text-sm text-muted-foreground">转载请注明出处</span><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 hover:text-primary" title="回到顶部"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up h-4 w-4" aria-hidden="true"><path d="m5 12 7-7 7 7"></path><path d="M12 19V5"></path></svg></button></div></div></div></div></footer><nav class="fixed bottom-0 left-0 right-0 z-40 bg-background/95 backdrop-blur-md border-t border-border safe-area-inset-bottom md:hidden" style="transform:translateY(100px)"><div class="flex items-center justify-around px-4 py-2"><a class="flex flex-col items-center justify-center p-2 rounded-lg transition-colors relative text-muted-foreground hover:text-foreground" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house h-5 w-5 mb-1 relative z-10" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg><span class="text-xs font-medium relative z-10">首页</span></a><a class="flex flex-col items-center justify-center p-2 rounded-lg transition-colors relative text-muted-foreground hover:text-foreground" href="/posts/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open h-5 w-5 mb-1 relative z-10" aria-hidden="true"><path d="M12 7v14"></path><path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"></path></svg><span class="text-xs font-medium relative z-10">文章</span></a><a class="flex flex-col items-center justify-center p-2 rounded-lg transition-colors relative text-muted-foreground hover:text-foreground" href="/tags/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-tag h-5 w-5 mb-1 relative z-10" aria-hidden="true"><path d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"></path><circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle></svg><span class="text-xs font-medium relative z-10">标签</span></a><a class="flex flex-col items-center justify-center p-2 rounded-lg transition-colors relative text-muted-foreground hover:text-foreground" href="/about/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user h-5 w-5 mb-1 relative z-10" aria-hidden="true"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg><span class="text-xs font-medium relative z-10">关于</span></a></div></nav></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-6e2a6b61b0a26b22.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[1362,[\"177\",\"static/chunks/app/layout-c1633986ddcf7869.js\"],\"ThemeProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[6453,[\"177\",\"static/chunks/app/layout-c1633986ddcf7869.js\"],\"default\"]\n7:I[9665,[],\"OutletBoundary\"]\n9:I[4911,[],\"AsyncMetadataOutlet\"]\nb:I[9665,[],\"ViewportBoundary\"]\nd:I[9665,[],\"MetadataBoundary\"]\ne:\"$Sreact.suspense\"\n10:I[8393,[],\"\"]\n:HL[\"/_next/static/media/bb3ef058b751a6ad-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/e7c402068c19d32e.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"J6PK5-nEpwEaPIFt4i3Gz\",\"p\":\"\",\"c\":[\"\",\"post\",\"1\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"post\",{\"children\":[[\"number\",\"1\",\"d\"],{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/e7c402068c19d32e.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_e8ce0c __variable_3c557b font-sans antialiased\",\"children\":[\"$\",\"$L2\",null,{\"attribute\":\"class\",\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L5\",null,{}]]}]}]}]]}],{\"children\":[\"post\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[[\"number\",\"1\",\"d\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L6\",null,[\"$\",\"$L7\",null,{\"children\":[\"$L8\",[\"$\",\"$L9\",null,{\"promise\":\"$@a\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$Lb\",null,{\"children\":\"$Lc\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]],[\"$\",\"$Ld\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$e\",null,{\"fallback\":null,\"children\":\"$Lf\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$10\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n8:null\n"])</script><script>self.__next_f.push([1,"11:I[6705,[\"458\",\"static/chunks/e58627ac-a79280295dcf1433.js\",\"707\",\"static/chunks/707-81b7d3c46238013e.js\",\"788\",\"static/chunks/788-6e98c5cada96323d.js\",\"729\",\"static/chunks/app/post/%5Bnumber%5D/page-ec2f9ee14f1143da.js\"],\"default\"]\n12:I[8175,[],\"IconMark\"]\n6:[\"$\",\"$L11\",null,{\"post\":{\"id\":2369562566,\"title\":\"第一篇博客测试\",\"body\":\"测试能不能成功22\",\"labels\":[],\"created_at\":\"2024-06-24T08:19:49Z\",\"updated_at\":\"2024-07-16T16:51:24Z\",\"html_url\":\"https://github.com/hepingfly/hepingfly.github.io/issues/1\",\"number\":1,\"comments\":1,\"user\":{\"login\":\"hepingfly\",\"avatar_url\":\"https://avatars.githubusercontent.com/u/18638517?v=4\"},\"excerpt\":\"测试能不能成功22\",\"readingTime\":1,\"slug\":\"\"}}]\n"])</script><script>self.__next_f.push([1,"a:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"第一篇博客测试 | 和平自留地 | 和平自留地\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"测试能不能成功22\"}],[\"$\",\"link\",\"2\",{\"rel\":\"author\",\"href\":\"https://hepingfly.github.io\"}],[\"$\",\"meta\",\"3\",{\"name\":\"author\",\"content\":\"和平\"}],[\"$\",\"meta\",\"4\",{\"name\":\"keywords\",\"content\":\"个人博客, 个人IP, 读书分享, 思维成长, Next.js, 和平自留地\"}],[\"$\",\"meta\",\"5\",{\"name\":\"creator\",\"content\":\"和平\"}],[\"$\",\"meta\",\"6\",{\"name\":\"publisher\",\"content\":\"和平\"}],[\"$\",\"meta\",\"7\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"8\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"9\",{\"name\":\"category\",\"content\":\"个人博客\"}],[\"$\",\"meta\",\"10\",{\"name\":\"google-site-verification\",\"content\":\"your-google-verification-code\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:title\",\"content\":\"第一篇博客测试 | 和平自留地\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:description\",\"content\":\"测试能不能成功22\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:url\",\"content\":\"https://hepingfly.github.io/post/1/\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:site_name\",\"content\":\"和平自留地\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:locale\",\"content\":\"zh_CN\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image\",\"content\":\"https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:image:alt\",\"content\":\"第一篇博客测试\"}],[\"$\",\"meta\",\"20\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"21\",{\"property\":\"article:published_time\",\"content\":\"2024-06-24T08:19:49Z\"}],[\"$\",\"meta\",\"22\",{\"property\":\"article:modified_time\",\"content\":\"2024-07-16T16:51:24Z\"}],[\"$\",\"meta\",\"23\",{\"property\":\"article:author\",\"content\":\"和平\"}],[\"$\",\"meta\",\"24\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"25\",{\"name\":\"twitter:creator\",\"content\":\"@hepingfly\"}],[\"$\",\"meta\",\"26\",{\"name\":\"twitter:title\",\"content\":\"第一篇博客测试 | 和平自留地\"}],[\"$\",\"meta\",\"27\",{\"name\":\"twitter:description\",\"content\":\"测试能不能成功22\"}],[\"$\",\"meta\",\"28\",{\"name\":\"twitter:image\",\"content\":\"https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG\"}],[\"$\",\"link\",\"29\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L12\",\"30\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"f:\"$a:metadata\"\n"])</script></body></html>