(()=>{var a={};a.id=626,a.ids=[626],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57303:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{GET:()=>x,dynamic:()=>w});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(69345);let w="force-static";async function x(){try{let a=(await (0,v.zX)()).slice(0,20).map(a=>{let b=new Date(a.created_at).toUTCString(),c=`${v.wj.siteUrl}/post/${a.number}`;return`
    <item>
      <title><![CDATA[${a.title}]]></title>
      <description><![CDATA[${a.excerpt||""}]]></description>
      <link>${c}</link>
      <guid>${c}</guid>
      <pubDate>${b}</pubDate>
      <author><EMAIL> (和平)</author>
      ${a.labels.map(a=>`<category>${a}</category>`).join("\n      ")}
    </item>`}).join(""),b=`<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>${v.wj.title}</title>
    <description>${v.wj.subtitle}</description>
    <link>${v.wj.siteUrl}</link>
    <language>zh-CN</language>
    <managingEditor><EMAIL> (和平)</managingEditor>
    <webMaster><EMAIL> (和平)</webMaster>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <atom:link href="${v.wj.siteUrl}/rss.xml" rel="self" type="application/rss+xml"/>
    ${a}
  </channel>
</rss>`;return new u.NextResponse(b,{headers:{"Content-Type":"application/xml","Cache-Control":"public, s-maxage=3600, stale-while-revalidate=86400"}})}catch(b){console.error("Error generating RSS feed:",b);let a=`<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
  <channel>
    <title>${v.wj.title}</title>
    <description>${v.wj.subtitle}</description>
    <link>${v.wj.siteUrl}</link>
    <language>zh-CN</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
  </channel>
</rss>`;return new u.NextResponse(a,{headers:{"Content-Type":"application/xml","Cache-Control":"public, s-maxage=3600, stale-while-revalidate=86400"}})}}let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/rss.xml/route",pathname:"/rss.xml",filename:"route",bundlePath:"app/rss.xml/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/rss.xml/route.ts",nextConfigOutput:"export",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/rss.xml/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69345:(a,b,c)=>{"use strict";c.d(b,{wj:()=>m,zX:()=>n,r:()=>p,zl:()=>o,Pf:()=>q});var d=c(68373);class e{set(a,b,c=3e5){this.cache.set(a,{data:b,timestamp:Date.now(),ttl:c})}get(a){let b=this.cache.get(a);return b?Date.now()-b.timestamp>b.ttl?(this.cache.delete(a),null):b.data:null}has(a){let b=this.cache.get(a);return!!b&&(!(Date.now()-b.timestamp>b.ttl)||(this.cache.delete(a),!1))}delete(a){return this.cache.delete(a)}clear(){this.cache.clear()}getStats(){let a=Date.now(),b=0,c=0;for(let[,d]of this.cache.entries())a-d.timestamp>d.ttl?c++:b++;return{total:this.cache.size,valid:b,expired:c}}cleanup(){let a=Date.now(),b=[];for(let[c,d]of this.cache.entries())a-d.timestamp>d.ttl&&b.push(c);b.forEach(a=>this.cache.delete(a))}constructor(){this.cache=new Map}}let f=new e,g={ALL_POSTS:"all_posts",POST_BY_NUMBER:a=>`post_${a}`,POSTS_BY_TAG:a=>`posts_tag_${a}`,ALL_TAGS:"all_tags"};function h(a,b,c=3e5){return async(...d)=>{let e=b(...d),g=f.get(e);if(null!==g)return g;let h=await a(...d);return f.set(e,h,c),h}}function i(a,b=200){let c=a.replace(/#{1,6}\s+/g,"").replace(/\*\*(.*?)\*\*/g,"$1").replace(/\*(.*?)\*/g,"$1").replace(/`(.*?)`/g,"$1").replace(/```[\s\S]*?```/g,"").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1").replace(/!\[([^\]]*)\]\([^)]+\)/g,"").replace(/\n+/g," ").trim();if(c.length<=b)return c;let d=c.substring(0,b),e=d.lastIndexOf(" ");return e>.8*b?d.substring(0,e)+"...":d+"..."}function j(a){let b=(a.match(/[\u4e00-\u9fff]/g)||[]).length;return Math.max(1,Math.round(b/300+a.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(a=>a.length>0).length/200))}function k(a){return a.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}setInterval(()=>{f.cleanup()},6e5),c(99379);let l=new d.E({auth:process.env.GITHUB_TOKEN}),m={owner:"hepingfly",repo:"hepingfly.github.io",title:"和平自留地",subtitle:"如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹",avatarUrl:"https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG",siteUrl:"https://hepingfly.github.io"},n=h(async function(){try{let{data:a}=await l.rest.issues.listForRepo({owner:m.owner,repo:m.repo,state:"open",sort:"created",direction:"desc",per_page:100});return a.filter(a=>!a.pull_request).map(a=>{let b=a.body||"";return{id:a.id,title:a.title,body:b,labels:a.labels.map(a=>"string"==typeof a?a:a.name||""),created_at:a.created_at,updated_at:a.updated_at,html_url:a.html_url,number:a.number,comments:a.comments,user:{login:a.user?.login||"",avatar_url:a.user?.avatar_url||""},excerpt:i(b),readingTime:j(b),slug:k(a.title)}})}catch(a){throw console.error("Error fetching posts:",a),Error(`Failed to fetch posts: ${a instanceof Error?a.message:"Unknown error"}`)}},()=>g.ALL_POSTS,6e5),o=h(async function(a){try{let{data:b}=await l.rest.issues.get({owner:m.owner,repo:m.repo,issue_number:a});if(b.pull_request)return null;let c=b.body||"";return{id:b.id,title:b.title,body:c,labels:b.labels.map(a=>"string"==typeof a?a:a.name||""),created_at:b.created_at,updated_at:b.updated_at,html_url:b.html_url,number:b.number,comments:b.comments,user:{login:b.user?.login||"",avatar_url:b.user?.avatar_url||""},excerpt:i(c),readingTime:j(c),slug:k(b.title)}}catch(b){if(console.error("Error fetching post:",b),b instanceof Error&&b.message.includes("404"))return null;throw Error(`Failed to fetch post ${a}: ${b instanceof Error?b.message:"Unknown error"}`)}},a=>g.POST_BY_NUMBER(a),9e5),p=h(async function(){let a=await n(),b=new Set;return a.forEach(a=>{a.labels.forEach(a=>{a&&b.add(a)})}),Array.from(b).sort()},()=>g.ALL_TAGS,6e5),q=h(async function(a){return(await n()).filter(b=>b.labels.includes(a))},a=>g.POSTS_BY_TAG(a),6e5)},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,223,436],()=>b(b.s=57303));module.exports=c})();