(()=>{var a={};a.id=530,a.ids=[530],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22493:(a,b,c)=>{"use strict";c.d(b,{A:()=>t,y:()=>u});var d=c(60687);c(43210);var e=c(85814),f=c.n(e),g=c(92576),h=c(82080),i=c(37360),j=c(40228),k=c(48730),l=c(33872),m=c(70334),n=c(29523),o=c(96834),p=c(44493),q=c(35950),r=c(64152);let s={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5,ease:"easeOut"}}};function t({post:a,variant:b="default",showExcerpt:c=!0,className:e=""}){let t="compact"===b,u="featured"===b;return(0,d.jsx)(g.P.div,{variants:s,initial:"hidden",whileInView:"visible",viewport:{once:!0},className:`group ${e}`,children:(0,d.jsxs)(p.Zp,{className:`h-full card-hover border-border/50 hover:border-primary/20 ${u?"bg-gradient-to-br from-primary/5 to-primary/10 border-primary/30":""}`,children:[(0,d.jsxs)(p.aR,{className:`space-y-4 ${t?"pb-3":""}`,children:[u&&(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsxs)(o.E,{className:"bg-primary text-primary-foreground",children:[(0,d.jsx)(h.A,{className:"h-3 w-3 mr-1"}),"精选文章"]})}),a.labels.length>0&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[a.labels.slice(0,t?1:3).map(a=>(0,d.jsx)(o.E,{variant:"outline",className:"text-xs hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer",asChild:!0,children:(0,d.jsxs)(f(),{href:`/tag/${encodeURIComponent(a)}`,children:[(0,d.jsx)(i.A,{className:"h-2.5 w-2.5 mr-1"}),a]})},a)),a.labels.length>(t?1:3)&&(0,d.jsxs)(o.E,{variant:"outline",className:"text-xs",children:["+",a.labels.length-(t?1:3)]})]}),(0,d.jsx)("h3",{className:`font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2 ${u?"text-2xl":t?"text-lg":"text-xl"}`,children:(0,d.jsx)(f(),{href:`/post/${a.number}`,className:"hover:underline",children:a.title})})]}),(0,d.jsxs)(p.Wu,{className:"space-y-4",children:[c&&a.excerpt&&(0,d.jsx)("p",{className:`text-muted-foreground leading-relaxed ${t?"line-clamp-2 text-sm":"line-clamp-3"}`,children:a.excerpt}),!t&&(0,d.jsx)(q.w,{}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-sm text-muted-foreground",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(j.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{className:"text-xs sm:text-sm",children:(0,r.fw)(a.created_at)})]}),a.readingTime&&(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(k.A,{className:"h-3 w-3"}),(0,d.jsxs)("span",{className:"text-xs sm:text-sm",children:[a.readingTime,"分钟"]})]})]}),(0,d.jsx)("div",{className:"flex items-center space-x-3",children:a.comments>0&&(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(l.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{className:"text-xs sm:text-sm",children:a.comments})]})})]}),!t&&(0,d.jsx)(n.$,{variant:"ghost",size:"sm",className:"w-full group/btn justify-between mt-4",asChild:!0,children:(0,d.jsxs)(f(),{href:`/post/${a.number}`,children:[(0,d.jsx)("span",{children:"阅读全文"}),(0,d.jsx)(m.A,{className:"h-4 w-4 group-hover/btn:translate-x-1 transition-transform"})]})})]})]})})}function u({variant:a="default"}){let b="compact"===a;return(0,d.jsxs)(p.Zp,{className:"h-full animate-pulse",children:[(0,d.jsxs)(p.aR,{className:`space-y-4 ${b?"pb-3":""}`,children:[(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("div",{className:"h-5 bg-muted rounded-full w-16"}),(0,d.jsx)("div",{className:"h-5 bg-muted rounded-full w-20"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-6 bg-muted rounded w-full"}),(0,d.jsx)("div",{className:"h-6 bg-muted rounded w-3/4"})]})]}),(0,d.jsxs)(p.Wu,{className:"space-y-4",children:[!b&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded w-full"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded w-full"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded w-2/3"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded w-16"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded w-12"})]}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded w-8"})]}),!b&&(0,d.jsx)("div",{className:"h-8 bg-muted rounded w-full"})]})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33872:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]])},33873:a=>{"use strict";a.exports=require("path")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41320:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/posts/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/posts/page.tsx","default")},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},53093:(a,b,c)=>{Promise.resolve().then(c.bind(c,41320))},57325:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>w});var d=c(60687),e=c(43210),f=c.n(e),g=c(16189),h=c(92576),i=c(82080),j=c(99270),k=c(62688);let l=(0,k.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),m=(0,k.A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),n=(0,k.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var o=c(29523),p=c(89667),q=c(96834),r=c(95874),s=c(22493),t=c(62901);let u={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}};function v(){(0,g.useSearchParams)();let[a,b]=(0,e.useState)("grid"),[c,k]=(0,e.useState)(""),{data:v,loading:w,error:x}=(0,t.iV)(),{data:y,loading:z}=(0,t.cU)(),{query:A,setQuery:B,debouncedQuery:C,data:D,loading:E}=(0,t.VP)(),F=f().useMemo(()=>{let a=v||[];return C&&(a=D||[]),c&&(a=a.filter(a=>a.labels.includes(c))),a},[v,D,C,c]),G=w||E;return(0,d.jsx)(r.default,{children:(0,d.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,d.jsx)("section",{className:"py-12 bg-muted/30",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)(h.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,d.jsx)(i.A,{className:"h-6 w-6 text-primary"}),(0,d.jsx)(q.E,{variant:"secondary",children:"全部文章"})]}),(0,d.jsx)("h1",{className:"text-4xl font-bold text-foreground",children:"文章列表"}),(0,d.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"探索所有文章，发现有价值的内容和思考"}),(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-8 pt-4",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary",children:v?.length||0}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"篇文章"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary",children:y?.length||0}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"个标签"})]})]})]})})}),(0,d.jsx)("section",{className:"py-8 border-b border-border",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(p.p,{placeholder:"搜索文章...",value:A,onChange:a=>B(a.target.value),className:"pl-10"})]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(o.$,{variant:"grid"===a?"default":"outline",size:"sm",onClick:()=>b("grid"),children:(0,d.jsx)(l,{className:"h-4 w-4"})}),(0,d.jsx)(o.$,{variant:"list"===a?"default":"outline",size:"sm",onClick:()=>b("list"),children:(0,d.jsx)(m,{className:"h-4 w-4"})})]})]}),!z&&y&&y.length>0&&(0,d.jsxs)(h.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},className:"mt-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,d.jsx)(n,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"按标签筛选:"})]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,d.jsx)(o.$,{variant:""===c?"default":"outline",size:"sm",onClick:()=>k(""),children:"全部"}),y.map(a=>(0,d.jsx)(o.$,{variant:c===a?"default":"outline",size:"sm",onClick:()=>k(a),children:a},a))]})]})]})}),(0,d.jsx)("section",{className:"py-12",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[G&&(0,d.jsx)(h.P.div,{variants:u,initial:"hidden",animate:"visible",className:`grid gap-8 ${"grid"===a?"grid-cols-1 md:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:[1,2,3,4,5,6].map(b=>(0,d.jsx)(s.y,{variant:"list"===a?"compact":"default"},b))}),x&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"加载文章时出现错误，请稍后再试"}),(0,d.jsx)(o.$,{onClick:()=>window.location.reload(),children:"重新加载"})]}),!G&&!x&&0===F.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(i.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:C||c?"没有找到匹配的文章":"暂无文章"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:C||c?"尝试调整搜索条件或筛选标签":"还没有发布任何文章"}),(C||c)&&(0,d.jsx)(o.$,{variant:"outline",onClick:()=>{B(""),k("")},children:"清除筛选"})]}),!G&&!x&&F.length>0&&(0,d.jsx)(h.P.div,{variants:u,initial:"hidden",animate:"visible",className:`grid gap-8 ${"grid"===a?"grid-cols-1 md:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:F.map(b=>(0,d.jsx)(s.A,{post:b,variant:"list"===a?"compact":"default",showExcerpt:"grid"===a},b.id))})]})})]})})}function w(){return(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(r.default,{children:(0,d.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"加载中..."})]})})}),children:(0,d.jsx)(v,{})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77075:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["posts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,41320)),"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/posts/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/posts/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/posts/page",pathname:"/posts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/posts/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89541:(a,b,c)=>{Promise.resolve().then(c.bind(c,57325))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,223,238,290,44,874],()=>b(b.s=77075));module.exports=c})();