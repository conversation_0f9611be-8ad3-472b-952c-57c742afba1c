(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5736:(a,b,c)=>{Promise.resolve().then(c.bind(c,97684)),Promise.resolve().then(c.bind(c,70797)),Promise.resolve().then(c.bind(c,59651)),Promise.resolve().then(c.bind(c,14143))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14143:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/Layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/Layout.tsx","default")},15807:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},18110:(a,b,c)=>{"use strict";c.d(b,{default:()=>w});var d=c(60687);c(43210);var e=c(85814),f=c.n(e),g=c(92576),h=c(82080),i=c(25541),j=c(62688);let k=(0,j.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),l=(0,j.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]);var m=c(13166),n=c(67760);let o=(0,j.A)("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]]);var p=c(70334),q=c(29523),r=c(96834),s=c(32584),t=c(23991);let u=[{label:"文章数量",value:"4+",icon:h.A},{label:"访问量",value:"7.5K+",icon:i.A},{label:"读者",value:"100+",icon:k}],v=[{text:"个人IP打造",color:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},{text:"读书分享",color:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},{text:"思维成长",color:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"}];function w(){return(0,d.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 gradient-bg"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,d.jsx)(g.P.div,{className:"absolute top-20 left-10 text-primary/20",animate:{y:[0,-20,0],rotate:[0,5,0]},transition:{duration:6,repeat:1/0,ease:"easeInOut"},children:(0,d.jsx)(l,{className:"h-8 w-8"})}),(0,d.jsx)(g.P.div,{className:"absolute top-32 right-16 text-primary/20",animate:{y:[0,20,0],rotate:[0,-5,0]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},children:(0,d.jsx)(m.A,{className:"h-6 w-6"})}),(0,d.jsx)(g.P.div,{className:"absolute bottom-32 left-20 text-primary/20",animate:{y:[0,-15,0],rotate:[0,10,0]},transition:{duration:7,repeat:1/0,ease:"easeInOut"},children:(0,d.jsx)(n.A,{className:"h-7 w-7"})}),(0,d.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,d.jsxs)(g.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-6",children:[(0,d.jsx)(g.P.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.6,delay:.2,type:"spring",stiffness:200},children:(0,d.jsxs)(s.eu,{className:"h-24 w-24 ring-4 ring-primary/20 ring-offset-4 ring-offset-background",children:[(0,d.jsx)(s.BK,{src:t.wj.avatarUrl,alt:t.wj.title}),(0,d.jsx)(s.q5,{className:"text-2xl font-bold",children:"和"})]})}),(0,d.jsx)(g.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"space-y-2",children:(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,d.jsx)(o,{className:"h-5 w-5 text-primary"}),(0,d.jsx)(r.E,{variant:"secondary",className:"text-sm",children:"个人品牌建设者"})]})})]}),(0,d.jsxs)(g.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"space-y-4",children:[(0,d.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight",children:["欢迎来到",(0,d.jsx)("span",{className:"text-primary block sm:inline sm:ml-3",children:t.wj.title})]}),(0,d.jsx)("p",{className:"text-xl sm:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:t.wj.subtitle})]}),(0,d.jsx)(g.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.8},className:"flex flex-wrap items-center justify-center gap-3",children:v.map((a,b)=>(0,d.jsx)(g.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.4,delay:.8+.1*b},children:(0,d.jsx)(r.E,{className:a.color,children:a.text})},a.text))}),(0,d.jsxs)(g.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:1},className:"flex flex-col sm:flex-row items-center justify-center gap-4",children:[(0,d.jsx)(q.$,{size:"lg",asChild:!0,className:"group",children:(0,d.jsxs)(f(),{href:"/posts",children:[(0,d.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"开始阅读",(0,d.jsx)(p.A,{className:"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform"})]})}),(0,d.jsx)(q.$,{size:"lg",variant:"outline",asChild:!0,children:(0,d.jsx)(f(),{href:"/about",children:"了解更多"})})]}),(0,d.jsx)(g.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:1.2},className:"grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto pt-8",children:u.map((a,b)=>(0,d.jsxs)(g.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:1.2+.1*b},className:"text-center space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center",children:[(0,d.jsx)(a.icon,{className:"h-5 w-5 text-primary mr-2"}),(0,d.jsx)("span",{className:"text-2xl font-bold text-foreground",children:a.value})]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:a.label})]},a.label))})]})}),(0,d.jsx)(g.P.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:1.5},children:(0,d.jsxs)(g.P.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},className:"flex flex-col items-center space-y-2 text-muted-foreground",children:[(0,d.jsx)("span",{className:"text-sm",children:"向下滚动"}),(0,d.jsx)("div",{className:"w-px h-8 bg-gradient-to-b from-muted-foreground to-transparent"})]})})]})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(37413),e=c(59651),f=c(70797),g=c(97684),h=c(14143);function i(){return(0,d.jsxs)(h.default,{children:[(0,d.jsx)(e.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)(g.default,{})]})}},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33872:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]])},33873:a=>{"use strict";a.exports=require("path")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},59651:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>","default")},62955:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,21204)),"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70203:(a,b,c)=>{Promise.resolve().then(c.bind(c,76431)),Promise.resolve().then(c.bind(c,77523)),Promise.resolve().then(c.bind(c,18110)),Promise.resolve().then(c.bind(c,95874))},70797:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>","default")},76431:(a,b,c)=>{"use strict";c.d(b,{default:()=>v});var d=c(60687);c(43210);var e=c(85814),f=c.n(e),g=c(92576),h=c(62688);let i=(0,h.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var j=c(82080),k=c(15807),l=c(58869);let m=(0,h.A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]]);var n=c(70334);let o=(0,h.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var p=c(29523),q=c(44493),r=c(32584),s=c(23991);let t=[{icon:i,title:"个人品牌建设",description:"分享个人IP打造的底层逻辑和实战经验，帮助每个人都能拥有自己的个人品牌。",color:"text-blue-600 dark:text-blue-400"},{icon:j.A,title:"读书心得分享",description:"深度解读优质书籍，分享阅读感悟，探讨如何通过阅读实现个人成长。",color:"text-green-600 dark:text-green-400"},{icon:k.A,title:"思维模式升级",description:"探索高效的思维方式和学习方法，分享认知升级的心得体会。",color:"text-purple-600 dark:text-purple-400"}],u=[{label:"专注领域",value:"3+"},{label:"文章发布",value:"4+"},{label:"持续更新",value:"2024"}];function v(){return(0,d.jsx)("section",{className:"py-20 bg-muted/30",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,d.jsxs)(g.P.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(l.A,{className:"h-5 w-5 text-primary"}),(0,d.jsx)("span",{className:"text-sm font-medium text-primary",children:"关于博主"})]}),(0,d.jsxs)("h2",{className:"text-3xl sm:text-4xl font-bold text-foreground",children:["用文字记录成长",(0,d.jsx)("br",{}),(0,d.jsx)("span",{className:"text-primary",children:"分享价值思考"})]}),(0,d.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed",children:"我是一个热爱学习和分享的人，专注于个人品牌建设、读书心得和思维成长。 希望通过这个博客，能够与更多志同道合的朋友交流学习，共同进步。"})]}),(0,d.jsx)(g.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"relative",children:(0,d.jsx)(q.Zp,{className:"border-l-4 border-l-primary bg-primary/5",children:(0,d.jsxs)(q.Wu,{className:"p-6",children:[(0,d.jsx)(m,{className:"h-6 w-6 text-primary mb-3"}),(0,d.jsx)("blockquote",{className:"text-foreground font-medium italic",children:"“如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹”"})]})})}),(0,d.jsx)(g.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"grid grid-cols-3 gap-6",children:u.map((a,b)=>(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary mb-1",children:a.value}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.label})]},a.label))}),(0,d.jsx)(g.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},children:(0,d.jsx)(p.$,{size:"lg",asChild:!0,className:"group",children:(0,d.jsxs)(f(),{href:"/about",children:["了解更多关于我",(0,d.jsx)(n.A,{className:"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform"})]})})})]}),(0,d.jsxs)(g.P.div,{initial:{opacity:0,x:20},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"space-y-6",children:[(0,d.jsx)(q.Zp,{className:"p-6 text-center bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20",children:(0,d.jsxs)(q.Wu,{className:"space-y-4",children:[(0,d.jsx)(g.P.div,{whileHover:{scale:1.05},transition:{duration:.2},children:(0,d.jsxs)(r.eu,{className:"h-20 w-20 mx-auto ring-4 ring-primary/20 ring-offset-4 ring-offset-background",children:[(0,d.jsx)(r.BK,{src:s.wj.avatarUrl,alt:"博主头像"}),(0,d.jsx)(r.q5,{className:"text-xl font-bold",children:"和"})]})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-1",children:"和平"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"个人品牌建设者 \xb7 终身学习者"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[[1,2,3,4,5].map(a=>(0,d.jsx)(o,{className:"h-4 w-4 text-yellow-400 fill-current"},a)),(0,d.jsx)("span",{className:"text-sm text-muted-foreground ml-2",children:"持续创作中"})]})]})}),(0,d.jsx)("div",{className:"space-y-4",children:t.map(a=>(0,d.jsx)(g.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.3},viewport:{once:!0},children:(0,d.jsx)(q.Zp,{className:"p-4 hover:shadow-md transition-shadow",children:(0,d.jsxs)(q.Wu,{className:"flex items-start space-x-4",children:[(0,d.jsx)("div",{className:`p-2 rounded-lg bg-muted ${a.color}`,children:(0,d.jsx)(a.icon,{className:"h-5 w-5"})}),(0,d.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,d.jsx)("h4",{className:"font-semibold text-foreground",children:a.title}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:a.description})]})]})})},a.title))})]})]})})})}},77523:(a,b,c)=>{"use strict";c.d(b,{default:()=>u});var d=c(60687);c(43210);var e=c(85814),f=c.n(e),g=c(92576),h=c(25541),i=c(40228),j=c(48730),k=c(33872),l=c(70334),m=c(29523),n=c(96834),o=c(44493),p=c(35950),q=c(62901),r=c(64152);let s={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},t={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};function u(){let{data:a,loading:b,error:c}=(0,q.AC)(3);return b?(0,d.jsx)("section",{className:"py-20 bg-background",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold text-foreground mb-4",children:"最新文章"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"探索最新的思考和分享"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[1,2,3].map(a=>(0,d.jsxs)(o.Zp,{className:"animate-pulse",children:[(0,d.jsxs)(o.aR,{children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded w-3/4 mb-2"}),(0,d.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]}),(0,d.jsx)(o.Wu,{children:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-3 bg-muted rounded"}),(0,d.jsx)("div",{className:"h-3 bg-muted rounded"}),(0,d.jsx)("div",{className:"h-3 bg-muted rounded w-2/3"})]})})]},a))})]})}):c||!a?(0,d.jsx)("section",{className:"py-20 bg-background",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,d.jsx)("p",{className:"text-muted-foreground",children:"暂时无法加载文章，请稍后再试"})})}):(0,d.jsx)("section",{className:"py-20 bg-background",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(g.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,d.jsx)(h.A,{className:"h-5 w-5 text-primary"}),(0,d.jsx)(n.E,{variant:"secondary",children:"最新发布"})]}),(0,d.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-foreground mb-4",children:"最新文章"}),(0,d.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"探索最新的思考和分享，一起成长进步"})]}),(0,d.jsx)(g.P.div,{variants:s,initial:"hidden",whileInView:"visible",viewport:{once:!0},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:a.map(a=>(0,d.jsx)(g.P.div,{variants:t,className:"group",children:(0,d.jsxs)(o.Zp,{className:"h-full card-hover border-border/50 hover:border-primary/20",children:[(0,d.jsxs)(o.aR,{className:"space-y-4",children:[a.labels.length>0&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[a.labels.slice(0,2).map(a=>(0,d.jsx)(n.E,{variant:"outline",className:"text-xs",children:a},a)),a.labels.length>2&&(0,d.jsxs)(n.E,{variant:"outline",className:"text-xs",children:["+",a.labels.length-2]})]}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2",children:(0,d.jsx)(f(),{href:`/post/${a.number}`,children:a.title})})]}),(0,d.jsxs)(o.Wu,{className:"space-y-4",children:[(0,d.jsx)("p",{className:"text-muted-foreground line-clamp-3 leading-relaxed",children:a.excerpt||"暂无摘要..."}),(0,d.jsx)(p.w,{}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(i.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:(0,r.fw)(a.created_at)})]}),a.readingTime&&(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(j.A,{className:"h-3 w-3"}),(0,d.jsxs)("span",{children:[a.readingTime,"分钟"]})]})]}),(0,d.jsx)("div",{className:"flex items-center space-x-3",children:a.comments>0&&(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(k.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:a.comments})]})})]}),(0,d.jsx)(m.$,{variant:"ghost",size:"sm",className:"w-full group/btn justify-between",asChild:!0,children:(0,d.jsxs)(f(),{href:`/post/${a.number}`,children:[(0,d.jsx)("span",{children:"阅读全文"}),(0,d.jsx)(l.A,{className:"h-4 w-4 group-hover/btn:translate-x-1 transition-transform"})]})})]})]})},a.id))}),(0,d.jsx)(g.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"text-center mt-12",children:(0,d.jsx)(m.$,{size:"lg",variant:"outline",asChild:!0,className:"group",children:(0,d.jsxs)(f(),{href:"/posts",children:["查看所有文章",(0,d.jsx)(l.A,{className:"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform"})]})})})]})})}},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},97684:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>","default")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,223,238,290,44,874],()=>b(b.s=62955));module.exports=c})();