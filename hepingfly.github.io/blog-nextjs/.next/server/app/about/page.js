(()=>{var a={};a.id=220,a.ids=[220],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14108:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/about/AboutPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/about/AboutPageClient.tsx","default")},15807:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},16090:(a,b,c)=>{Promise.resolve().then(c.bind(c,97854))},18579:(a,b,c)=>{"use strict";c.d(b,{YQ:()=>e,iT:()=>g,kn:()=>f});var d=c(69345);function e({title:a,description:b,keywords:c=[],image:e,url:f,type:g="website",publishedTime:h,modifiedTime:i,author:j="和平",tags:k=[]}){let l=a?`${a} | ${d.wj.title}`:d.wj.title,m=b||d.wj.subtitle,n=f||d.wj.siteUrl,o=e||d.wj.avatarUrl,p={title:l,description:m,keywords:["个人博客","个人IP","读书分享","思维成长","Next.js","和平自留地",...c,...k].join(", "),authors:[{name:j,url:d.wj.siteUrl}],creator:j,publisher:j,openGraph:{type:g,locale:"zh_CN",url:n,title:l,description:m,siteName:d.wj.title,images:[{url:o,width:1200,height:630,alt:a||d.wj.title}]},twitter:{card:"summary_large_image",title:l,description:m,images:[o],creator:"@hepingfly"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"},category:"个人博客"};return"article"===g&&h&&(p.openGraph={...p.openGraph,type:"article",publishedTime:h,modifiedTime:i||h,authors:[j],tags:k}),p}function f(a){return e({title:a.title,description:a.excerpt||`${a.title} - 来自${d.wj.title}的文章`,keywords:a.labels,url:`${d.wj.siteUrl}/post/${a.number}`,type:"article",publishedTime:a.created_at,modifiedTime:a.updated_at,tags:a.labels})}function g(a,b){return e({title:`标签: ${a}`,description:`浏览所有关于"${a}"的文章，共${b}篇文章 - ${d.wj.title}`,keywords:[a,"标签","分类"],url:`${d.wj.siteUrl}/tag/${encodeURIComponent(a)}`})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28770:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g,metadata:()=>f});var d=c(37413);c(61120);var e=c(14108);let f=(0,c(18579).YQ)({title:"关于我",description:"了解更多关于和平的信息，个人品牌建设者，终身学习者，专注于个人IP打造和读书分享",keywords:["关于","个人简介","和平","个人品牌","终身学习"],url:"https://hepingfly.github.io/about"});function g(){return(0,d.jsx)(e.default,{})}},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},52882:(a,b,c)=>{Promise.resolve().then(c.bind(c,14108))},55467:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,28770)),"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/about/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/about/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/about/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},97854:(a,b,c)=>{"use strict";c.d(b,{default:()=>w});var d=c(60687);c(43210);var e=c(92576),f=c(82080),g=c(58869),h=c(15807),i=c(67760),j=c(41550),k=c(62157),l=c(13166),m=c(48224),n=c(29523),o=c(44493),p=c(96834),q=c(32584),r=c(95874),s=c(23991);let t=["个人品牌建设","内容创作","读书分享","思维模式","学习方法","认知升级"],u=[{name:"阅读",icon:f.A,description:"热爱阅读各类书籍，特别是个人成长和思维类"},{name:"写作",icon:g.A,description:"通过写作分享思考和感悟，记录成长历程"},{name:"学习",icon:h.A,description:"保持终身学习的心态，不断探索新知识"},{name:"分享",icon:i.A,description:"乐于分享有价值的内容和经验"}],v=[{year:"2024",title:"开始个人IP建设",description:'创建"和平自留地"博客，专注于个人品牌建设和读书分享'},{year:"2023",title:"深度阅读实践",description:"开始系统性阅读个人成长类书籍，并记录读书心得"},{year:"2022",title:"思维模式探索",description:"开始关注思维模式和认知升级，探索高效学习方法"}];function w(){return(0,d.jsx)(r.default,{children:(0,d.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,d.jsx)("section",{className:"py-20 bg-muted/30",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center space-y-8",children:[(0,d.jsxs)(q.eu,{className:"h-32 w-32 mx-auto ring-4 ring-primary/20 ring-offset-4 ring-offset-background",children:[(0,d.jsx)(q.BK,{src:s.wj.avatarUrl,alt:"和平"}),(0,d.jsx)(q.q5,{className:"text-4xl font-bold",children:"和"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h1",{className:"text-4xl sm:text-5xl font-bold text-foreground",children:"你好，我是和平"}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed",children:"个人品牌建设者 \xb7 终身学习者 \xb7 内容创作者"}),(0,d.jsx)("div",{className:"flex flex-wrap justify-center gap-2 pt-4",children:t.map(a=>(0,d.jsx)(p.E,{variant:"secondary",children:a},a))})]}),(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,d.jsx)(n.$,{asChild:!0,children:(0,d.jsxs)("a",{href:"mailto:<EMAIL>",children:[(0,d.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"联系我"]})}),(0,d.jsx)(n.$,{variant:"outline",asChild:!0,children:(0,d.jsxs)("a",{href:"https://github.com/hepingfly",target:"_blank",rel:"noopener noreferrer",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"GitHub"]})})]})]})})}),(0,d.jsx)("section",{className:"py-20",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 space-y-16",children:[(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:(0,d.jsxs)(o.Zp,{children:[(0,d.jsx)(o.aR,{children:(0,d.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(g.A,{className:"h-5 w-5 text-primary"}),(0,d.jsx)("span",{children:"关于我"})]})}),(0,d.jsxs)(o.Wu,{className:"prose prose-lg max-w-none dark:prose-invert",children:[(0,d.jsx)("p",{children:"欢迎来到我的个人空间！我是一个热爱学习和分享的人，专注于个人品牌建设、读书心得和思维成长。"}),(0,d.jsx)("p",{children:"我相信每个人都可以拥有自己的个人品牌，通过持续的学习和分享，我们可以不断提升自己的认知水平， 实现个人价值的最大化。"}),(0,d.jsx)("p",{children:"在这个博客中，我会分享我的读书心得、思考感悟，以及个人品牌建设的实践经验。 希望这些内容能够对你有所帮助，也欢迎与我交流讨论。"})]})]})}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,d.jsx)("h2",{className:"text-3xl font-bold text-foreground mb-8 text-center",children:"我的兴趣"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:u.map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(o.Zp,{className:"h-full hover:shadow-lg transition-shadow",children:(0,d.jsx)(o.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)("div",{className:"p-2 bg-primary/10 rounded-lg",children:(0,d.jsx)(a.icon,{className:"h-6 w-6 text-primary"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold text-foreground mb-2",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:a.description})]})]})})})},a.name))})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,d.jsx)("h2",{className:"text-3xl font-bold text-foreground mb-8 text-center",children:"成长历程"}),(0,d.jsx)("div",{className:"space-y-6",children:v.map((a,b)=>(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5,delay:.1*b},viewport:{once:!0},className:"flex items-start space-x-4",children:[(0,d.jsx)("div",{className:"flex-shrink-0 w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-sm font-bold text-primary",children:a.year})}),(0,d.jsxs)("div",{className:"flex-1 pb-8",children:[(0,d.jsx)("h3",{className:"font-semibold text-foreground mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-muted-foreground",children:a.description})]})]},a.year))})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:(0,d.jsx)(o.Zp,{className:"text-center",children:(0,d.jsxs)(o.Wu,{className:"p-8",children:[(0,d.jsx)(l.A,{className:"h-12 w-12 text-primary mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-4",children:"让我们保持联系"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-6",children:"如果你对我的内容感兴趣，或者想要交流讨论，欢迎通过以下方式联系我"}),(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,d.jsx)(n.$,{asChild:!0,children:(0,d.jsxs)("a",{href:"mailto:<EMAIL>",children:[(0,d.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"发邮件"]})}),(0,d.jsx)(n.$,{variant:"outline",asChild:!0,children:(0,d.jsxs)("a",{href:"/rss.xml",target:"_blank",rel:"noopener noreferrer",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"订阅RSS"]})})]})]})})})]})})]})})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,223,238,290,44,874],()=>b(b.s=55467));module.exports=c})();