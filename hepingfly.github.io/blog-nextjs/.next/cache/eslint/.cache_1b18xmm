[{"/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/layout.tsx": "1", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/page.tsx": "2", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/avatar.tsx": "3", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/badge.tsx": "4", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/button.tsx": "5", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/card.tsx": "6", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/navigation-menu.tsx": "7", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/separator.tsx": "8", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/github.ts": "9", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/markdown.ts": "10", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/utils.ts": "11", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/types/blog.ts": "12", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/hooks/useBlog.ts": "13", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/cache.ts": "14", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/Footer.tsx": "15", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/Header.tsx": "16", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/Layout.tsx": "17", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/design-system.ts": "18", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>": "21", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/posts/page.tsx": "22", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/tag/[slug]/page.tsx": "23", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/tags/page.tsx": "24", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/blog/PostCard.tsx": "25", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/input.tsx": "26", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/tag/[slug]/TagPageClient.tsx": "27", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/post/[number]/PostPageClient.tsx": "28", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/post/[number]/page.tsx": "29", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/MobileBottomNav.tsx": "30", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/search/MobileSearch.tsx": "31", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/responsive-container.tsx": "32", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/search/AdvancedSearch.tsx": "33", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/search/SearchHighlight.tsx": "34", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/tags/TagCloud.tsx": "35", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/about/AboutPageClient.tsx": "36", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/about/page.tsx": "37", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/robots.ts": "38", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/rss.xml/route.ts": "39", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/sitemap.ts": "40", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/analytics/Analytics.tsx": "41", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/seo/SEOHead.tsx": "42", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/lazy-load.tsx": "43", "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/optimized-image.tsx": "44"}, {"size": 2008, "mtime": 1754320702451, "results": "45", "hashOfConfig": "46"}, {"size": 374, "mtime": 1754318586439, "results": "47", "hashOfConfig": "46"}, {"size": 1097, "mtime": 1754317228011, "results": "48", "hashOfConfig": "46"}, {"size": 1631, "mtime": 1754317228009, "results": "49", "hashOfConfig": "46"}, {"size": 2123, "mtime": 1754317227994, "results": "50", "hashOfConfig": "46"}, {"size": 1989, "mtime": 1754317228006, "results": "51", "hashOfConfig": "46"}, {"size": 6664, "mtime": 1754317228018, "results": "52", "hashOfConfig": "46"}, {"size": 699, "mtime": 1754317228019, "results": "53", "hashOfConfig": "46"}, {"size": 9499, "mtime": 1754320085220, "results": "54", "hashOfConfig": "46"}, {"size": 3484, "mtime": 1754317293867, "results": "55", "hashOfConfig": "46"}, {"size": 166, "mtime": 1754317215715, "results": "56", "hashOfConfig": "46"}, {"size": 1811, "mtime": 1754317309140, "results": "57", "hashOfConfig": "46"}, {"size": 3523, "mtime": 1754317630530, "results": "58", "hashOfConfig": "46"}, {"size": 2890, "mtime": 1754317745845, "results": "59", "hashOfConfig": "46"}, {"size": 6448, "mtime": 1754318035433, "results": "60", "hashOfConfig": "46"}, {"size": 7192, "mtime": 1754319736951, "results": "61", "hashOfConfig": "46"}, {"size": 1021, "mtime": 1754319902320, "results": "62", "hashOfConfig": "46"}, {"size": 5953, "mtime": 1754317860057, "results": "63", "hashOfConfig": "46"}, {"size": 7931, "mtime": 1754320957545, "results": "64", "hashOfConfig": "46"}, {"size": 7777, "mtime": 1754318774787, "results": "65", "hashOfConfig": "46"}, {"size": 8198, "mtime": 1754318478249, "results": "66", "hashOfConfig": "46"}, {"size": 10242, "mtime": 1754320333488, "results": "67", "hashOfConfig": "46"}, {"size": 1515, "mtime": 1754320603778, "results": "68", "hashOfConfig": "46"}, {"size": 7795, "mtime": 1754320134700, "results": "69", "hashOfConfig": "46"}, {"size": 6908, "mtime": 1754319761491, "results": "70", "hashOfConfig": "46"}, {"size": 967, "mtime": 1754318897021, "results": "71", "hashOfConfig": "46"}, {"size": 6144, "mtime": 1754319377017, "results": "72", "hashOfConfig": "46"}, {"size": 12935, "mtime": 1754320227517, "results": "73", "hashOfConfig": "46"}, {"size": 1803, "mtime": 1754320557888, "results": "74", "hashOfConfig": "46"}, {"size": 2084, "mtime": 1754319878207, "results": "75", "hashOfConfig": "46"}, {"size": 6348, "mtime": 1754320201764, "results": "76", "hashOfConfig": "46"}, {"size": 837, "mtime": 1754319633603, "results": "77", "hashOfConfig": "46"}, {"size": 9073, "mtime": 1754320185043, "results": "78", "hashOfConfig": "46"}, {"size": 927, "mtime": 1754320062804, "results": "79", "hashOfConfig": "46"}, {"size": 2818, "mtime": 1754320106175, "results": "80", "hashOfConfig": "46"}, {"size": 10342, "mtime": 1754320842054, "results": "81", "hashOfConfig": "46"}, {"size": 588, "mtime": 1754320767249, "results": "82", "hashOfConfig": "46"}, {"size": 347, "mtime": 1754321063955, "results": "83", "hashOfConfig": "46"}, {"size": 2352, "mtime": 1754321093225, "results": "84", "hashOfConfig": "46"}, {"size": 2305, "mtime": 1754321078412, "results": "85", "hashOfConfig": "46"}, {"size": 4244, "mtime": 1754320944566, "results": "86", "hashOfConfig": "46"}, {"size": 5045, "mtime": 1754321005305, "results": "87", "hashOfConfig": "46"}, {"size": 1386, "mtime": 1754320645791, "results": "88", "hashOfConfig": "46"}, {"size": 1958, "mtime": 1754320634064, "results": "89", "hashOfConfig": "46"}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "17v91ci", {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/page.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/button.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/card.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/navigation-menu.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/github.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/markdown.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/types/blog.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/hooks/useBlog.ts", [], ["222", "223"], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/cache.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/lib/design-system.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>", ["224"], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/home/<USER>", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/posts/page.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/tag/[slug]/page.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/tags/page.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/blog/PostCard.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/input.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/tag/[slug]/TagPageClient.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/post/[number]/PostPageClient.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/post/[number]/page.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/layout/MobileBottomNav.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/search/MobileSearch.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/responsive-container.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/search/AdvancedSearch.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/search/SearchHighlight.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/tags/TagCloud.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/about/AboutPageClient.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/about/page.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/robots.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/rss.xml/route.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/app/sitemap.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/analytics/Analytics.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/seo/SEOHead.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/lazy-load.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/blog/hepingfly.github.io/blog-nextjs/src/components/ui/optimized-image.tsx", [], [], {"ruleId": "225", "severity": 1, "message": "226", "line": 56, "column": 6, "nodeType": "227", "endLine": 56, "endColumn": 10, "suppressions": "228"}, {"ruleId": "225", "severity": 1, "message": "229", "line": 56, "column": 6, "nodeType": "227", "endLine": 56, "endColumn": 10, "suggestions": "230", "suppressions": "231"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 105, "column": 33, "nodeType": null, "messageId": "234", "endLine": 105, "endColumn": 38}, "react-hooks/exhaustive-deps", "React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies.", "Identifier", ["235"], "React Hook useEffect has a missing dependency: 'asyncFn'. Either include it or remove the dependency array. If 'asyncFn' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["236"], ["237"], "@typescript-eslint/no-unused-vars", "'index' is defined but never used.", "unusedVar", {"kind": "238", "justification": "239"}, {"desc": "240", "fix": "241"}, {"kind": "238", "justification": "239"}, "directive", "", "Update the dependencies array to be: [asyncFn]", {"range": "242", "text": "243"}, [1191, 1195], "[asyncFn]"]