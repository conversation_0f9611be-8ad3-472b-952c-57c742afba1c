"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-content-type-parse";
exports.ids = ["vendor-chunks/fast-content-type-parse"];
exports.modules = {

/***/ "(rsc)/./node_modules/fast-content-type-parse/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/fast-content-type-parse/index.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("\n\nconst NullObject = function NullObject () { }\nNullObject.prototype = Object.create(null)\n\n/**\n * RegExp to match *( \";\" parameter ) in RFC 7231 sec 3.1.1.1\n *\n * parameter     = token \"=\" ( token / quoted-string )\n * token         = 1*tchar\n * tchar         = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" / \"*\"\n *               / \"+\" / \"-\" / \".\" / \"^\" / \"_\" / \"`\" / \"|\" / \"~\"\n *               / DIGIT / ALPHA\n *               ; any VCHAR, except delimiters\n * quoted-string = DQUOTE *( qdtext / quoted-pair ) DQUOTE\n * qdtext        = HTAB / SP / %x21 / %x23-5B / %x5D-7E / obs-text\n * obs-text      = %x80-FF\n * quoted-pair   = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n */\nconst paramRE = /; *([!#$%&'*+.^\\w`|~-]+)=(\"(?:[\\v\\u0020\\u0021\\u0023-\\u005b\\u005d-\\u007e\\u0080-\\u00ff]|\\\\[\\v\\u0020-\\u00ff])*\"|[!#$%&'*+.^\\w`|~-]+) */gu\n\n/**\n * RegExp to match quoted-pair in RFC 7230 sec 3.2.6\n *\n * quoted-pair = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n * obs-text    = %x80-FF\n */\nconst quotedPairRE = /\\\\([\\v\\u0020-\\u00ff])/gu\n\n/**\n * RegExp to match type in RFC 7231 sec 3.1.1.1\n *\n * media-type = type \"/\" subtype\n * type       = token\n * subtype    = token\n */\nconst mediaTypeRE = /^[!#$%&'*+.^\\w|~-]+\\/[!#$%&'*+.^\\w|~-]+$/u\n\n// default ContentType to prevent repeated object creation\nconst defaultContentType = { type: '', parameters: new NullObject() }\nObject.freeze(defaultContentType.parameters)\nObject.freeze(defaultContentType)\n\n/**\n * Parse media type to object.\n *\n * @param {string|object} header\n * @return {Object}\n * @public\n */\n\nfunction parse (header) {\n  if (typeof header !== 'string') {\n    throw new TypeError('argument header is required and must be a string')\n  }\n\n  let index = header.indexOf(';')\n  const type = index !== -1\n    ? header.slice(0, index).trim()\n    : header.trim()\n\n  if (mediaTypeRE.test(type) === false) {\n    throw new TypeError('invalid media type')\n  }\n\n  const result = {\n    type: type.toLowerCase(),\n    parameters: new NullObject()\n  }\n\n  // parse parameters\n  if (index === -1) {\n    return result\n  }\n\n  let key\n  let match\n  let value\n\n  paramRE.lastIndex = index\n\n  while ((match = paramRE.exec(header))) {\n    if (match.index !== index) {\n      throw new TypeError('invalid parameter format')\n    }\n\n    index += match[0].length\n    key = match[1].toLowerCase()\n    value = match[2]\n\n    if (value[0] === '\"') {\n      // remove quotes and escapes\n      value = value\n        .slice(1, value.length - 1)\n\n      quotedPairRE.test(value) && (value = value.replace(quotedPairRE, '$1'))\n    }\n\n    result.parameters[key] = value\n  }\n\n  if (index !== header.length) {\n    throw new TypeError('invalid parameter format')\n  }\n\n  return result\n}\n\nfunction safeParse (header) {\n  if (typeof header !== 'string') {\n    return defaultContentType\n  }\n\n  let index = header.indexOf(';')\n  const type = index !== -1\n    ? header.slice(0, index).trim()\n    : header.trim()\n\n  if (mediaTypeRE.test(type) === false) {\n    return defaultContentType\n  }\n\n  const result = {\n    type: type.toLowerCase(),\n    parameters: new NullObject()\n  }\n\n  // parse parameters\n  if (index === -1) {\n    return result\n  }\n\n  let key\n  let match\n  let value\n\n  paramRE.lastIndex = index\n\n  while ((match = paramRE.exec(header))) {\n    if (match.index !== index) {\n      return defaultContentType\n    }\n\n    index += match[0].length\n    key = match[1].toLowerCase()\n    value = match[2]\n\n    if (value[0] === '\"') {\n      // remove quotes and escapes\n      value = value\n        .slice(1, value.length - 1)\n\n      quotedPairRE.test(value) && (value = value.replace(quotedPairRE, '$1'))\n    }\n\n    result.parameters[key] = value\n  }\n\n  if (index !== header.length) {\n    return defaultContentType\n  }\n\n  return result\n}\n\nmodule.exports[\"default\"] = { parse, safeParse }\nmodule.exports.parse = parse\nmodule.exports.safeParse = safeParse\nmodule.exports.defaultContentType = defaultContentType\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-content-type-parse/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-content-type-parse/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/fast-content-type-parse/index.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("\n\nconst NullObject = function NullObject () { }\nNullObject.prototype = Object.create(null)\n\n/**\n * RegExp to match *( \";\" parameter ) in RFC 7231 sec 3.1.1.1\n *\n * parameter     = token \"=\" ( token / quoted-string )\n * token         = 1*tchar\n * tchar         = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" / \"*\"\n *               / \"+\" / \"-\" / \".\" / \"^\" / \"_\" / \"`\" / \"|\" / \"~\"\n *               / DIGIT / ALPHA\n *               ; any VCHAR, except delimiters\n * quoted-string = DQUOTE *( qdtext / quoted-pair ) DQUOTE\n * qdtext        = HTAB / SP / %x21 / %x23-5B / %x5D-7E / obs-text\n * obs-text      = %x80-FF\n * quoted-pair   = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n */\nconst paramRE = /; *([!#$%&'*+.^\\w`|~-]+)=(\"(?:[\\v\\u0020\\u0021\\u0023-\\u005b\\u005d-\\u007e\\u0080-\\u00ff]|\\\\[\\v\\u0020-\\u00ff])*\"|[!#$%&'*+.^\\w`|~-]+) */gu\n\n/**\n * RegExp to match quoted-pair in RFC 7230 sec 3.2.6\n *\n * quoted-pair = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n * obs-text    = %x80-FF\n */\nconst quotedPairRE = /\\\\([\\v\\u0020-\\u00ff])/gu\n\n/**\n * RegExp to match type in RFC 7231 sec 3.1.1.1\n *\n * media-type = type \"/\" subtype\n * type       = token\n * subtype    = token\n */\nconst mediaTypeRE = /^[!#$%&'*+.^\\w|~-]+\\/[!#$%&'*+.^\\w|~-]+$/u\n\n// default ContentType to prevent repeated object creation\nconst defaultContentType = { type: '', parameters: new NullObject() }\nObject.freeze(defaultContentType.parameters)\nObject.freeze(defaultContentType)\n\n/**\n * Parse media type to object.\n *\n * @param {string|object} header\n * @return {Object}\n * @public\n */\n\nfunction parse (header) {\n  if (typeof header !== 'string') {\n    throw new TypeError('argument header is required and must be a string')\n  }\n\n  let index = header.indexOf(';')\n  const type = index !== -1\n    ? header.slice(0, index).trim()\n    : header.trim()\n\n  if (mediaTypeRE.test(type) === false) {\n    throw new TypeError('invalid media type')\n  }\n\n  const result = {\n    type: type.toLowerCase(),\n    parameters: new NullObject()\n  }\n\n  // parse parameters\n  if (index === -1) {\n    return result\n  }\n\n  let key\n  let match\n  let value\n\n  paramRE.lastIndex = index\n\n  while ((match = paramRE.exec(header))) {\n    if (match.index !== index) {\n      throw new TypeError('invalid parameter format')\n    }\n\n    index += match[0].length\n    key = match[1].toLowerCase()\n    value = match[2]\n\n    if (value[0] === '\"') {\n      // remove quotes and escapes\n      value = value\n        .slice(1, value.length - 1)\n\n      quotedPairRE.test(value) && (value = value.replace(quotedPairRE, '$1'))\n    }\n\n    result.parameters[key] = value\n  }\n\n  if (index !== header.length) {\n    throw new TypeError('invalid parameter format')\n  }\n\n  return result\n}\n\nfunction safeParse (header) {\n  if (typeof header !== 'string') {\n    return defaultContentType\n  }\n\n  let index = header.indexOf(';')\n  const type = index !== -1\n    ? header.slice(0, index).trim()\n    : header.trim()\n\n  if (mediaTypeRE.test(type) === false) {\n    return defaultContentType\n  }\n\n  const result = {\n    type: type.toLowerCase(),\n    parameters: new NullObject()\n  }\n\n  // parse parameters\n  if (index === -1) {\n    return result\n  }\n\n  let key\n  let match\n  let value\n\n  paramRE.lastIndex = index\n\n  while ((match = paramRE.exec(header))) {\n    if (match.index !== index) {\n      return defaultContentType\n    }\n\n    index += match[0].length\n    key = match[1].toLowerCase()\n    value = match[2]\n\n    if (value[0] === '\"') {\n      // remove quotes and escapes\n      value = value\n        .slice(1, value.length - 1)\n\n      quotedPairRE.test(value) && (value = value.replace(quotedPairRE, '$1'))\n    }\n\n    result.parameters[key] = value\n  }\n\n  if (index !== header.length) {\n    return defaultContentType\n  }\n\n  return result\n}\n\nmodule.exports[\"default\"] = { parse, safeParse }\nmodule.exports.parse = parse\nmodule.exports.safeParse = safeParse\nmodule.exports.defaultContentType = defaultContentType\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-content-type-parse/index.js\n");

/***/ })

};
;