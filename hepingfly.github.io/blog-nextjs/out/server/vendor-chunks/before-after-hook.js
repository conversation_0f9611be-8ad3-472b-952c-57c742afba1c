"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/before-after-hook";
exports.ids = ["vendor-chunks/before-after-hook"];
exports.modules = {

/***/ "(rsc)/./node_modules/before-after-hook/index.js":
/*!*************************************************!*\
  !*** ./node_modules/before-after-hook/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_register_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/register.js */ \"(rsc)/./node_modules/before-after-hook/lib/register.js\");\n/* harmony import */ var _lib_add_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/add.js */ \"(rsc)/./node_modules/before-after-hook/lib/add.js\");\n/* harmony import */ var _lib_remove_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/remove.js */ \"(rsc)/./node_modules/before-after-hook/lib/remove.js\");\n// @ts-check\n\n\n\n\n\n// bind with array of arguments: https://stackoverflow.com/a/21792913\nconst bind = Function.bind;\nconst bindable = bind.bind(bind);\n\nfunction bindApi(hook, state, name) {\n  const removeHookRef = bindable(_lib_remove_js__WEBPACK_IMPORTED_MODULE_2__.removeHook, null).apply(\n    null,\n    name ? [state, name] : [state]\n  );\n  hook.api = { remove: removeHookRef };\n  hook.remove = removeHookRef;\n  [\"before\", \"error\", \"after\", \"wrap\"].forEach((kind) => {\n    const args = name ? [state, kind, name] : [state, kind];\n    hook[kind] = hook.api[kind] = bindable(_lib_add_js__WEBPACK_IMPORTED_MODULE_1__.addHook, null).apply(null, args);\n  });\n}\n\nfunction Singular() {\n  const singularHookName = Symbol(\"Singular\");\n  const singularHookState = {\n    registry: {},\n  };\n  const singularHook = _lib_register_js__WEBPACK_IMPORTED_MODULE_0__.register.bind(null, singularHookState, singularHookName);\n  bindApi(singularHook, singularHookState, singularHookName);\n  return singularHook;\n}\n\nfunction Collection() {\n  const state = {\n    registry: {},\n  };\n\n  const hook = _lib_register_js__WEBPACK_IMPORTED_MODULE_0__.register.bind(null, state);\n  bindApi(hook, state);\n\n  return hook;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({ Singular, Collection });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/before-after-hook/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/before-after-hook/lib/add.js":
/*!***************************************************!*\
  !*** ./node_modules/before-after-hook/lib/add.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addHook: () => (/* binding */ addHook)\n/* harmony export */ });\n// @ts-check\n\nfunction addHook(state, kind, name, hook) {\n  const orig = hook;\n  if (!state.registry[name]) {\n    state.registry[name] = [];\n  }\n\n  if (kind === \"before\") {\n    hook = (method, options) => {\n      return Promise.resolve()\n        .then(orig.bind(null, options))\n        .then(method.bind(null, options));\n    };\n  }\n\n  if (kind === \"after\") {\n    hook = (method, options) => {\n      let result;\n      return Promise.resolve()\n        .then(method.bind(null, options))\n        .then((result_) => {\n          result = result_;\n          return orig(result, options);\n        })\n        .then(() => {\n          return result;\n        });\n    };\n  }\n\n  if (kind === \"error\") {\n    hook = (method, options) => {\n      return Promise.resolve()\n        .then(method.bind(null, options))\n        .catch((error) => {\n          return orig(error, options);\n        });\n    };\n  }\n\n  state.registry[name].push({\n    hook: hook,\n    orig: orig,\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/before-after-hook/lib/add.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/before-after-hook/lib/register.js":
/*!********************************************************!*\
  !*** ./node_modules/before-after-hook/lib/register.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   register: () => (/* binding */ register)\n/* harmony export */ });\n// @ts-check\n\nfunction register(state, name, method, options) {\n  if (typeof method !== \"function\") {\n    throw new Error(\"method for before hook must be a function\");\n  }\n\n  if (!options) {\n    options = {};\n  }\n\n  if (Array.isArray(name)) {\n    return name.reverse().reduce((callback, name) => {\n      return register.bind(null, state, name, callback, options);\n    }, method)();\n  }\n\n  return Promise.resolve().then(() => {\n    if (!state.registry[name]) {\n      return method(options);\n    }\n\n    return state.registry[name].reduce((method, registered) => {\n      return registered.hook.bind(null, method, options);\n    }, method)();\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYmVmb3JlLWFmdGVyLWhvb2svbGliL3JlZ2lzdGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIIiwic291cmNlcyI6WyIvVXNlcnMvaGVwaW5nZmx5L0RvY3VtZW50cy93b3Jrc3BhY2Uvd29ya3NwYWNlX3ZzY29kZS9ibG9nL2hlcGluZ2ZseS5naXRodWIuaW8vYmxvZy1uZXh0anMvbm9kZV9tb2R1bGVzL2JlZm9yZS1hZnRlci1ob29rL2xpYi9yZWdpc3Rlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAdHMtY2hlY2tcblxuZXhwb3J0IGZ1bmN0aW9uIHJlZ2lzdGVyKHN0YXRlLCBuYW1lLCBtZXRob2QsIG9wdGlvbnMpIHtcbiAgaWYgKHR5cGVvZiBtZXRob2QgIT09IFwiZnVuY3Rpb25cIikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIm1ldGhvZCBmb3IgYmVmb3JlIGhvb2sgbXVzdCBiZSBhIGZ1bmN0aW9uXCIpO1xuICB9XG5cbiAgaWYgKCFvcHRpb25zKSB7XG4gICAgb3B0aW9ucyA9IHt9O1xuICB9XG5cbiAgaWYgKEFycmF5LmlzQXJyYXkobmFtZSkpIHtcbiAgICByZXR1cm4gbmFtZS5yZXZlcnNlKCkucmVkdWNlKChjYWxsYmFjaywgbmFtZSkgPT4ge1xuICAgICAgcmV0dXJuIHJlZ2lzdGVyLmJpbmQobnVsbCwgc3RhdGUsIG5hbWUsIGNhbGxiYWNrLCBvcHRpb25zKTtcbiAgICB9LCBtZXRob2QpKCk7XG4gIH1cblxuICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCkudGhlbigoKSA9PiB7XG4gICAgaWYgKCFzdGF0ZS5yZWdpc3RyeVtuYW1lXSkge1xuICAgICAgcmV0dXJuIG1ldGhvZChvcHRpb25zKTtcbiAgICB9XG5cbiAgICByZXR1cm4gc3RhdGUucmVnaXN0cnlbbmFtZV0ucmVkdWNlKChtZXRob2QsIHJlZ2lzdGVyZWQpID0+IHtcbiAgICAgIHJldHVybiByZWdpc3RlcmVkLmhvb2suYmluZChudWxsLCBtZXRob2QsIG9wdGlvbnMpO1xuICAgIH0sIG1ldGhvZCkoKTtcbiAgfSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/before-after-hook/lib/register.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/before-after-hook/lib/remove.js":
/*!******************************************************!*\
  !*** ./node_modules/before-after-hook/lib/remove.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeHook: () => (/* binding */ removeHook)\n/* harmony export */ });\n// @ts-check\n\nfunction removeHook(state, name, method) {\n  if (!state.registry[name]) {\n    return;\n  }\n\n  const index = state.registry[name]\n    .map((registered) => {\n      return registered.orig;\n    })\n    .indexOf(method);\n\n  if (index === -1) {\n    return;\n  }\n\n  state.registry[name].splice(index, 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYmVmb3JlLWFmdGVyLWhvb2svbGliL3JlbW92ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRU87QUFDUDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvaGVwaW5nZmx5L0RvY3VtZW50cy93b3Jrc3BhY2Uvd29ya3NwYWNlX3ZzY29kZS9ibG9nL2hlcGluZ2ZseS5naXRodWIuaW8vYmxvZy1uZXh0anMvbm9kZV9tb2R1bGVzL2JlZm9yZS1hZnRlci1ob29rL2xpYi9yZW1vdmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQHRzLWNoZWNrXG5cbmV4cG9ydCBmdW5jdGlvbiByZW1vdmVIb29rKHN0YXRlLCBuYW1lLCBtZXRob2QpIHtcbiAgaWYgKCFzdGF0ZS5yZWdpc3RyeVtuYW1lXSkge1xuICAgIHJldHVybjtcbiAgfVxuXG4gIGNvbnN0IGluZGV4ID0gc3RhdGUucmVnaXN0cnlbbmFtZV1cbiAgICAubWFwKChyZWdpc3RlcmVkKSA9PiB7XG4gICAgICByZXR1cm4gcmVnaXN0ZXJlZC5vcmlnO1xuICAgIH0pXG4gICAgLmluZGV4T2YobWV0aG9kKTtcblxuICBpZiAoaW5kZXggPT09IC0xKSB7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgc3RhdGUucmVnaXN0cnlbbmFtZV0uc3BsaWNlKGluZGV4LCAxKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/before-after-hook/lib/remove.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/before-after-hook/index.js":
/*!*************************************************!*\
  !*** ./node_modules/before-after-hook/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_register_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/register.js */ \"(ssr)/./node_modules/before-after-hook/lib/register.js\");\n/* harmony import */ var _lib_add_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/add.js */ \"(ssr)/./node_modules/before-after-hook/lib/add.js\");\n/* harmony import */ var _lib_remove_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/remove.js */ \"(ssr)/./node_modules/before-after-hook/lib/remove.js\");\n// @ts-check\n\n\n\n\n\n// bind with array of arguments: https://stackoverflow.com/a/21792913\nconst bind = Function.bind;\nconst bindable = bind.bind(bind);\n\nfunction bindApi(hook, state, name) {\n  const removeHookRef = bindable(_lib_remove_js__WEBPACK_IMPORTED_MODULE_2__.removeHook, null).apply(\n    null,\n    name ? [state, name] : [state]\n  );\n  hook.api = { remove: removeHookRef };\n  hook.remove = removeHookRef;\n  [\"before\", \"error\", \"after\", \"wrap\"].forEach((kind) => {\n    const args = name ? [state, kind, name] : [state, kind];\n    hook[kind] = hook.api[kind] = bindable(_lib_add_js__WEBPACK_IMPORTED_MODULE_1__.addHook, null).apply(null, args);\n  });\n}\n\nfunction Singular() {\n  const singularHookName = Symbol(\"Singular\");\n  const singularHookState = {\n    registry: {},\n  };\n  const singularHook = _lib_register_js__WEBPACK_IMPORTED_MODULE_0__.register.bind(null, singularHookState, singularHookName);\n  bindApi(singularHook, singularHookState, singularHookName);\n  return singularHook;\n}\n\nfunction Collection() {\n  const state = {\n    registry: {},\n  };\n\n  const hook = _lib_register_js__WEBPACK_IMPORTED_MODULE_0__.register.bind(null, state);\n  bindApi(hook, state);\n\n  return hook;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({ Singular, Collection });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmVmb3JlLWFmdGVyLWhvb2svaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBOztBQUU2QztBQUNOO0FBQ007O0FBRTdDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGlDQUFpQyxzREFBVTtBQUMzQztBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLGdEQUFPO0FBQ2xELEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQSx1QkFBdUIsc0RBQVE7QUFDL0I7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7O0FBRUEsZUFBZSxzREFBUTtBQUN2Qjs7QUFFQTtBQUNBOztBQUVBLGlFQUFlLEVBQUUsc0JBQXNCLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9oZXBpbmdmbHkvRG9jdW1lbnRzL3dvcmtzcGFjZS93b3Jrc3BhY2VfdnNjb2RlL2Jsb2cvaGVwaW5nZmx5LmdpdGh1Yi5pby9ibG9nLW5leHRqcy9ub2RlX21vZHVsZXMvYmVmb3JlLWFmdGVyLWhvb2svaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQHRzLWNoZWNrXG5cbmltcG9ydCB7IHJlZ2lzdGVyIH0gZnJvbSBcIi4vbGliL3JlZ2lzdGVyLmpzXCI7XG5pbXBvcnQgeyBhZGRIb29rIH0gZnJvbSBcIi4vbGliL2FkZC5qc1wiO1xuaW1wb3J0IHsgcmVtb3ZlSG9vayB9IGZyb20gXCIuL2xpYi9yZW1vdmUuanNcIjtcblxuLy8gYmluZCB3aXRoIGFycmF5IG9mIGFyZ3VtZW50czogaHR0cHM6Ly9zdGFja292ZXJmbG93LmNvbS9hLzIxNzkyOTEzXG5jb25zdCBiaW5kID0gRnVuY3Rpb24uYmluZDtcbmNvbnN0IGJpbmRhYmxlID0gYmluZC5iaW5kKGJpbmQpO1xuXG5mdW5jdGlvbiBiaW5kQXBpKGhvb2ssIHN0YXRlLCBuYW1lKSB7XG4gIGNvbnN0IHJlbW92ZUhvb2tSZWYgPSBiaW5kYWJsZShyZW1vdmVIb29rLCBudWxsKS5hcHBseShcbiAgICBudWxsLFxuICAgIG5hbWUgPyBbc3RhdGUsIG5hbWVdIDogW3N0YXRlXVxuICApO1xuICBob29rLmFwaSA9IHsgcmVtb3ZlOiByZW1vdmVIb29rUmVmIH07XG4gIGhvb2sucmVtb3ZlID0gcmVtb3ZlSG9va1JlZjtcbiAgW1wiYmVmb3JlXCIsIFwiZXJyb3JcIiwgXCJhZnRlclwiLCBcIndyYXBcIl0uZm9yRWFjaCgoa2luZCkgPT4ge1xuICAgIGNvbnN0IGFyZ3MgPSBuYW1lID8gW3N0YXRlLCBraW5kLCBuYW1lXSA6IFtzdGF0ZSwga2luZF07XG4gICAgaG9va1traW5kXSA9IGhvb2suYXBpW2tpbmRdID0gYmluZGFibGUoYWRkSG9vaywgbnVsbCkuYXBwbHkobnVsbCwgYXJncyk7XG4gIH0pO1xufVxuXG5mdW5jdGlvbiBTaW5ndWxhcigpIHtcbiAgY29uc3Qgc2luZ3VsYXJIb29rTmFtZSA9IFN5bWJvbChcIlNpbmd1bGFyXCIpO1xuICBjb25zdCBzaW5ndWxhckhvb2tTdGF0ZSA9IHtcbiAgICByZWdpc3RyeToge30sXG4gIH07XG4gIGNvbnN0IHNpbmd1bGFySG9vayA9IHJlZ2lzdGVyLmJpbmQobnVsbCwgc2luZ3VsYXJIb29rU3RhdGUsIHNpbmd1bGFySG9va05hbWUpO1xuICBiaW5kQXBpKHNpbmd1bGFySG9vaywgc2luZ3VsYXJIb29rU3RhdGUsIHNpbmd1bGFySG9va05hbWUpO1xuICByZXR1cm4gc2luZ3VsYXJIb29rO1xufVxuXG5mdW5jdGlvbiBDb2xsZWN0aW9uKCkge1xuICBjb25zdCBzdGF0ZSA9IHtcbiAgICByZWdpc3RyeToge30sXG4gIH07XG5cbiAgY29uc3QgaG9vayA9IHJlZ2lzdGVyLmJpbmQobnVsbCwgc3RhdGUpO1xuICBiaW5kQXBpKGhvb2ssIHN0YXRlKTtcblxuICByZXR1cm4gaG9vaztcbn1cblxuZXhwb3J0IGRlZmF1bHQgeyBTaW5ndWxhciwgQ29sbGVjdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/before-after-hook/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/before-after-hook/lib/add.js":
/*!***************************************************!*\
  !*** ./node_modules/before-after-hook/lib/add.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addHook: () => (/* binding */ addHook)\n/* harmony export */ });\n// @ts-check\n\nfunction addHook(state, kind, name, hook) {\n  const orig = hook;\n  if (!state.registry[name]) {\n    state.registry[name] = [];\n  }\n\n  if (kind === \"before\") {\n    hook = (method, options) => {\n      return Promise.resolve()\n        .then(orig.bind(null, options))\n        .then(method.bind(null, options));\n    };\n  }\n\n  if (kind === \"after\") {\n    hook = (method, options) => {\n      let result;\n      return Promise.resolve()\n        .then(method.bind(null, options))\n        .then((result_) => {\n          result = result_;\n          return orig(result, options);\n        })\n        .then(() => {\n          return result;\n        });\n    };\n  }\n\n  if (kind === \"error\") {\n    hook = (method, options) => {\n      return Promise.resolve()\n        .then(method.bind(null, options))\n        .catch((error) => {\n          return orig(error, options);\n        });\n    };\n  }\n\n  state.registry[name].push({\n    hook: hook,\n    orig: orig,\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/before-after-hook/lib/add.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/before-after-hook/lib/register.js":
/*!********************************************************!*\
  !*** ./node_modules/before-after-hook/lib/register.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   register: () => (/* binding */ register)\n/* harmony export */ });\n// @ts-check\n\nfunction register(state, name, method, options) {\n  if (typeof method !== \"function\") {\n    throw new Error(\"method for before hook must be a function\");\n  }\n\n  if (!options) {\n    options = {};\n  }\n\n  if (Array.isArray(name)) {\n    return name.reverse().reduce((callback, name) => {\n      return register.bind(null, state, name, callback, options);\n    }, method)();\n  }\n\n  return Promise.resolve().then(() => {\n    if (!state.registry[name]) {\n      return method(options);\n    }\n\n    return state.registry[name].reduce((method, registered) => {\n      return registered.hook.bind(null, method, options);\n    }, method)();\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmVmb3JlLWFmdGVyLWhvb2svbGliL3JlZ2lzdGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIIiwic291cmNlcyI6WyIvVXNlcnMvaGVwaW5nZmx5L0RvY3VtZW50cy93b3Jrc3BhY2Uvd29ya3NwYWNlX3ZzY29kZS9ibG9nL2hlcGluZ2ZseS5naXRodWIuaW8vYmxvZy1uZXh0anMvbm9kZV9tb2R1bGVzL2JlZm9yZS1hZnRlci1ob29rL2xpYi9yZWdpc3Rlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAdHMtY2hlY2tcblxuZXhwb3J0IGZ1bmN0aW9uIHJlZ2lzdGVyKHN0YXRlLCBuYW1lLCBtZXRob2QsIG9wdGlvbnMpIHtcbiAgaWYgKHR5cGVvZiBtZXRob2QgIT09IFwiZnVuY3Rpb25cIikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIm1ldGhvZCBmb3IgYmVmb3JlIGhvb2sgbXVzdCBiZSBhIGZ1bmN0aW9uXCIpO1xuICB9XG5cbiAgaWYgKCFvcHRpb25zKSB7XG4gICAgb3B0aW9ucyA9IHt9O1xuICB9XG5cbiAgaWYgKEFycmF5LmlzQXJyYXkobmFtZSkpIHtcbiAgICByZXR1cm4gbmFtZS5yZXZlcnNlKCkucmVkdWNlKChjYWxsYmFjaywgbmFtZSkgPT4ge1xuICAgICAgcmV0dXJuIHJlZ2lzdGVyLmJpbmQobnVsbCwgc3RhdGUsIG5hbWUsIGNhbGxiYWNrLCBvcHRpb25zKTtcbiAgICB9LCBtZXRob2QpKCk7XG4gIH1cblxuICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCkudGhlbigoKSA9PiB7XG4gICAgaWYgKCFzdGF0ZS5yZWdpc3RyeVtuYW1lXSkge1xuICAgICAgcmV0dXJuIG1ldGhvZChvcHRpb25zKTtcbiAgICB9XG5cbiAgICByZXR1cm4gc3RhdGUucmVnaXN0cnlbbmFtZV0ucmVkdWNlKChtZXRob2QsIHJlZ2lzdGVyZWQpID0+IHtcbiAgICAgIHJldHVybiByZWdpc3RlcmVkLmhvb2suYmluZChudWxsLCBtZXRob2QsIG9wdGlvbnMpO1xuICAgIH0sIG1ldGhvZCkoKTtcbiAgfSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/before-after-hook/lib/register.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/before-after-hook/lib/remove.js":
/*!******************************************************!*\
  !*** ./node_modules/before-after-hook/lib/remove.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeHook: () => (/* binding */ removeHook)\n/* harmony export */ });\n// @ts-check\n\nfunction removeHook(state, name, method) {\n  if (!state.registry[name]) {\n    return;\n  }\n\n  const index = state.registry[name]\n    .map((registered) => {\n      return registered.orig;\n    })\n    .indexOf(method);\n\n  if (index === -1) {\n    return;\n  }\n\n  state.registry[name].splice(index, 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmVmb3JlLWFmdGVyLWhvb2svbGliL3JlbW92ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRU87QUFDUDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvaGVwaW5nZmx5L0RvY3VtZW50cy93b3Jrc3BhY2Uvd29ya3NwYWNlX3ZzY29kZS9ibG9nL2hlcGluZ2ZseS5naXRodWIuaW8vYmxvZy1uZXh0anMvbm9kZV9tb2R1bGVzL2JlZm9yZS1hZnRlci1ob29rL2xpYi9yZW1vdmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQHRzLWNoZWNrXG5cbmV4cG9ydCBmdW5jdGlvbiByZW1vdmVIb29rKHN0YXRlLCBuYW1lLCBtZXRob2QpIHtcbiAgaWYgKCFzdGF0ZS5yZWdpc3RyeVtuYW1lXSkge1xuICAgIHJldHVybjtcbiAgfVxuXG4gIGNvbnN0IGluZGV4ID0gc3RhdGUucmVnaXN0cnlbbmFtZV1cbiAgICAubWFwKChyZWdpc3RlcmVkKSA9PiB7XG4gICAgICByZXR1cm4gcmVnaXN0ZXJlZC5vcmlnO1xuICAgIH0pXG4gICAgLmluZGV4T2YobWV0aG9kKTtcblxuICBpZiAoaW5kZXggPT09IC0xKSB7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgc3RhdGUucmVnaXN0cnlbbmFtZV0uc3BsaWNlKGluZGV4LCAxKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/before-after-hook/lib/remove.js\n");

/***/ })

};
;