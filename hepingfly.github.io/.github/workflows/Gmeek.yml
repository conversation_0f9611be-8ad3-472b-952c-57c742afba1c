name: build Gmeek

on:
  workflow_dispatch:
  issues:
    types: [opened, edited]
  # schedule:  # 禁用定时任务，防止覆盖 UI 重构
  #   - cron: "0 16 * * *"
    
jobs:
  build:
    name: Generate blog
    runs-on: ubuntu-20.04
    if: ${{ github.event.repository.owner.id == github.event.sender.id || github.event_name == 'schedule' }}
    permissions: write-all
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Pages
        id: pages
        uses: actions/configure-pages@v4

      - name: Get config.json
        run: |
          echo "====== check config.josn file ======"
          cat config.json
          echo "====== check config.josn end  ======"
          sudo apt-get install jq

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.8

      - name: Clone source code
        run: |
          GMEEK_VERSION=$(jq -r ".GMEEK_VERSION" config.json)
          git clone https://github.com/Meekdai/Gmeek.git /opt/Gmeek;
          cd /opt/Gmeek/
          lastTag=$(git describe --tags `git rev-list --tags --max-count=1`)
          if [ $GMEEK_VERSION == 'last' ]; then git checkout $lastTag; else git checkout $GMEEK_VERSION; fi;

      - name: Install dependencies
        run: |
          pip install --upgrade pip
          pip install -r /opt/Gmeek/requirements.txt

      - name: Generate new html
        run: |
          cp -r ./* /opt/Gmeek/
          cd /opt/Gmeek/
          python Gmeek.py ${{ secrets.GITHUB_TOKEN }} ${{ github.repository }} --issue_number '${{ github.event.issue.number }}'
          cp -a /opt/Gmeek/docs ${{ github.workspace }} 
          cp -a /opt/Gmeek/backup ${{ github.workspace }} 
          cp /opt/Gmeek/blogBase.json ${{ github.workspace }} 
          
      - name: update html
        run: |
          git config --local user.email "$(jq -r ".email" config.json)"
          git config --local user.name "${{ github.repository_owner }}"
          git add .
          git commit -a -m '🎉auto update by Gmeek action' || echo "nothing to commit"
          git push || echo "nothing to push"
          sleep 3
          
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: 'docs/.'
          
  deploy:
    name: Deploy blog
    runs-on: ubuntu-20.04
    needs: build
    permissions:
      contents: write
      pages: write
      id-token: write
    concurrency:
      group: "pages"
      cancel-in-progress: false
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
